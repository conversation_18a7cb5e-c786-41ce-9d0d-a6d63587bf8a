# 宝塔面板部署Node.js支付服务器指南

## 🎯 部署概述

本指南将帮助您在安装了宝塔面板的Linux服务器上部署Node.js支付服务器项目。

## 📋 前置要求

### 宝塔面板环境检查
- ✅ 宝塔面板已安装
- ✅ Node.js环境已安装（版本 ≥ 16.0.0）
- ✅ PM2管理器已安装
- ✅ Nginx已安装
- ✅ SSL证书已配置（生产环境必需）

### 检查步骤
1. 登录宝塔面板
2. 进入 `软件商店` → `运行环境`
3. 确认以下软件已安装：
   - Node.js (推荐v18.x)
   - PM2管理器
   - Nginx

## 🚀 详细部署步骤

### 步骤1：创建项目目录

1. **在宝塔面板中创建网站**
   - 进入 `网站` → `添加站点`
   - 域名：`your-payment-domain.com`
   - 根目录：`/www/wwwroot/payment-server`
   - PHP版本：选择 `纯静态`

2. **设置目录权限**
   ```bash
   # 在宝塔终端中执行
   chown -R www:www /www/wwwroot/payment-server
   chmod -R 755 /www/wwwroot/payment-server
   ```

### 步骤2：上传项目文件

#### 方法1：使用宝塔文件管理器（推荐）
1. 进入 `文件` → 导航到 `/www/wwwroot/payment-server`
2. 将本地的 `payment-server` 文件夹内容上传到此目录
3. 如果是压缩包，上传后解压

#### 方法2：使用Git（推荐开发者）
1. 在宝塔终端中执行：
   ```bash
   cd /www/wwwroot
   git clone <your-repo-url> payment-server
   # 或者如果已有目录
   cd /www/wwwroot/payment-server
   git init
   git remote add origin <your-repo-url>
   git pull origin main
   ```

#### 方法3：使用SFTP工具
- 使用FileZilla、WinSCP等工具上传

### 步骤3：配置环境变量

1. **复制环境变量模板**
   ```bash
   cd /www/wwwroot/payment-server
   cp .env.example .env
   ```

2. **编辑环境变量**
   在宝塔面板文件管理中编辑 `.env` 文件：
   ```bash
   # 服务器配置
   PORT=3000
   NODE_ENV=production
   SERVER_DOMAIN=your-payment-domain.com
   
   # 微信小程序配置
   WECHAT_APPID=your_actual_appid
   WECHAT_SECRET=your_actual_secret
   
   # 微信支付配置
   WECHAT_PAY_APPID=your_actual_appid
   WECHAT_PAY_MCHID=your_actual_mchid
   WECHAT_PAY_SERIAL_NO=your_actual_serial_no
   WECHAT_PAY_APIV3_KEY=your_actual_apiv3_key
   WECHAT_PAY_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
   your_actual_private_key_content
   -----END PRIVATE KEY-----"
   
   # 支付回调地址
   PAYMENT_NOTIFY_URL=https://your-payment-domain.com/api/payment/notify
   
   # JWT配置
   JWT_SECRET=your_strong_jwt_secret_here
   JWT_EXPIRES_IN=24h
   ```

### 步骤4：安装项目依赖

1. **在宝塔终端中安装依赖**
   ```bash
   cd /www/wwwroot/payment-server
   npm install --production
   ```

2. **检查安装结果**
   ```bash
   # 检查node_modules目录是否创建
   ls -la node_modules
   ```

### 步骤5：使用PM2管理器部署

1. **打开PM2管理器**
   - 在宝塔面板左侧菜单找到 `PM2管理器`
   - 如果没有，请先在软件商店安装

2. **添加项目**
   - 点击 `添加项目`
   - 填写以下信息：
     - **项目名称**: `payment-server`
     - **启动文件**: `/www/wwwroot/payment-server/src/app.js`
     - **项目目录**: `/www/wwwroot/payment-server`
     - **运行模式**: `cluster` (集群模式，推荐)
     - **实例数量**: `2` (根据服务器配置调整)
     - **环境变量**: 可以在这里添加额外的环境变量

3. **启动项目**
   - 点击 `保存` 后，项目会自动启动
   - 在PM2管理器中可以看到项目状态

### 步骤6：配置Nginx反向代理

1. **编辑网站配置**
   - 进入 `网站` → 找到您的域名 → 点击 `设置`
   - 选择 `配置文件` 标签

2. **修改Nginx配置**
   ```nginx
   server {
       listen 80;
       server_name your-payment-domain.com;
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl http2;
       server_name your-payment-domain.com;
       
       # SSL证书配置（宝塔会自动配置）
       ssl_certificate /www/server/panel/vhost/cert/your-domain/fullchain.pem;
       ssl_certificate_key /www/server/panel/vhost/cert/your-domain/privkey.pem;
       
       # SSL安全配置
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
       ssl_prefer_server_ciphers off;
       ssl_session_cache shared:SSL:10m;
       
       # 反向代理到Node.js应用
       location / {
           proxy_pass http://127.0.0.1:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
           
           # 超时设置
           proxy_connect_timeout 60s;
           proxy_send_timeout 60s;
           proxy_read_timeout 60s;
       }
       
       # 日志配置
       access_log /www/wwwlogs/payment-server.access.log;
       error_log /www/wwwlogs/payment-server.error.log;
   }
   ```

3. **重载Nginx配置**
   - 点击 `保存` 后，宝塔会自动重载Nginx配置

### 步骤7：配置SSL证书

1. **申请SSL证书**
   - 在网站设置中选择 `SSL` 标签
   - 选择 `Let's Encrypt` 免费证书
   - 点击 `申请` 并等待完成

2. **强制HTTPS**
   - 开启 `强制HTTPS` 选项

### 步骤8：配置防火墙

1. **在宝塔面板配置防火墙**
   - 进入 `安全` → `防火墙`
   - 确保以下端口开放：
     - `80` (HTTP)
     - `443` (HTTPS)
     - `22` (SSH)

2. **关闭Node.js端口**
   - 确保 `3000` 端口不对外开放（仅内部访问）

## 🔧 测试部署

### 1. 健康检查
```bash
# 在宝塔终端中测试
curl http://localhost:3000/health
curl https://your-payment-domain.com/health
```

### 2. PM2状态检查
在PM2管理器中查看：
- 项目状态应为 `online`
- CPU和内存使用正常
- 没有错误日志

### 3. 日志查看
- **应用日志**: PM2管理器中查看
- **Nginx日志**: `/www/wwwlogs/` 目录
- **应用文件日志**: `/www/wwwroot/payment-server/logs/`

## 📱 小程序配置

### 1. 更新API地址
编辑小程序中的 `utils/nodePaymentConfig.js`：
```javascript
const getApiBaseUrl = () => {
  // 正式版
  return 'https://your-payment-domain.com';
};
```

### 2. 配置服务器域名
在微信小程序管理后台：
- 进入 `开发` → `开发管理` → `开发设置`
- 在 `服务器域名` 中添加：
  - request合法域名: `https://your-payment-domain.com`

## 🔍 故障排除

### 常见问题

1. **项目无法启动**
   - 检查PM2管理器中的错误日志
   - 确认端口3000没有被占用
   - 检查环境变量配置

2. **SSL证书问题**
   - 重新申请Let's Encrypt证书
   - 检查域名DNS解析

3. **支付回调失败**
   - 确认回调URL可以从外网访问
   - 检查防火墙设置
   - 查看Nginx错误日志

### 调试命令
```bash
# 查看PM2进程
pm2 list

# 查看应用日志
pm2 logs payment-server

# 重启应用
pm2 restart payment-server

# 查看端口占用
netstat -tlnp | grep 3000

# 测试本地连接
curl http://localhost:3000/health
```

## 🔄 更新部署

### 代码更新流程
1. **停止应用**
   ```bash
   pm2 stop payment-server
   ```

2. **更新代码**
   ```bash
   cd /www/wwwroot/payment-server
   git pull origin main
   # 或重新上传文件
   ```

3. **安装新依赖**
   ```bash
   npm install --production
   ```

4. **重启应用**
   ```bash
   pm2 restart payment-server
   ```

## 📊 监控和维护

### 1. 设置监控
- 在宝塔面板中设置服务器监控
- 配置异常告警通知

### 2. 日志管理
- 定期清理日志文件
- 设置日志轮转

### 3. 备份策略
- 定期备份项目文件
- 备份环境变量配置

## 🎯 性能优化

### 1. PM2集群模式
- 根据服务器CPU核心数调整实例数量
- 监控内存使用情况

### 2. Nginx优化
- 启用gzip压缩
- 配置缓存策略
- 优化worker进程数

### 3. 数据库优化
- 如果使用独立数据库，优化连接池
- 添加适当的索引

通过以上步骤，您的Node.js支付服务器就可以在宝塔面板环境中成功部署运行了！
