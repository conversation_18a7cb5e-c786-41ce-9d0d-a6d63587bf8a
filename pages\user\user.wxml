<!--pages/user/user.wxml-->
<page-meta>
  <navigation-bar title="个人中心" back="{{false}}" extClass="custom-nav"></navigation-bar>
</page-meta>

<!-- 树叶飘落动画效果 -->
<view class="leaves-container" wx:if="{{leavesVisible}}" style="z-index: {{leavesZIndex}};">
  <view 
    class="leaf" 
    wx:for="{{leaves}}" 
    wx:key="id" 
    style="--fall-delay: {{item.delay}}s; --fall-duration: {{item.duration}}s; --leaf-size: {{item.size}}rpx; --start-pos: {{item.startPos}}%;"
    bind:animationend="onLeafAnimationEnd"
    data-leaf-id="{{item.id}}">
    <image 
      class="leaf-image" 
      src="{{item.type === 1 ? '/images/leaf1.png' : '/images/leaf2.png'}}"
      mode="aspectFit"
    ></image>
  </view>
</view>

<view class="page-container">
  <scroll-view class="scrollable-content" scroll-y="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}" bounces="{{true}}">
    <!-- 将header移到scroll-view内部 -->
    <view class="header">
      <view class="header-content" bind:tap="{{!isLogined ? 'onTapAvatar' : ''}}">
        <view class="user-profile">
          <view class="avatar-container">
            <t-avatar
              class="avatar-image"
              icon="{{userInfo.avatarUrl ? '' : 'user'}}"
              image="{{userInfo.avatarUrl || ''}}"
              size="large"
              catch:tap="onTapAvatar"
              shape="circle"
            />

            <view class="member-badge {{getLevelClass(userInfo.level)}}" wx:if="{{isLogined}}">
              <view class="member-badge-inner">
                <text class="member-prefix">LV</text>
                <text class="member-level">{{userInfo.level || 0}}</text>
              </view>
            </view>
            <view class="level-halo {{getLevelClass(userInfo.level)}}" wx:if="{{isLogined}}"></view>
          </view>
          <view class="user-info">
          
            <view class="user-name">
              <t-icon wx:if="{{isLogined}}" name="logo-wechat-stroke" size="32rpx" color="#fff"></t-icon>
              <text wx:if="{{isLogined}}" style=" font-weight: 600;color: white; left">:</text>
              <text class="{{isLogined ? 'user-name-text' : 'login-prompt-text'}}">{{userInfo.nickName || '未登录，点击该区域登陆'}}</text>
            </view>
            <view class="user-meta">
              <text wx:if="{{isLogined}}">{{userInfo.city || ''}}</text>
              <text wx:else>点击头像微信快捷登录</text>
            </view>
            <!-- 用户电话号码 -->
            <view class="user-phone" wx:if="{{isLogined && userInfo.phoneNumber}}">
              <t-icon name="call-1" size="30rpx" color="#fff"></t-icon>
              <text style=" font-weight:600;">:</text>
              <text class="phone-number">{{userInfo.phoneNumber}}</text>
            </view>
              <!-- #if 经验显示-->
          
              <!-- #endif -->
              <!-- <view class="user-exp-info" wx:if="{{isLogined}}">
                <view class="exp-header">
                  <text class="exp-prefix">LV.{{userInfo.level || 0}}</text>
                  <text class="exp-value">{{userInfo.exp || 0}}/{{userInfo.level >= 6 ? 'MAX' : (userInfo.level == 0 ? 50 : (userInfo.level == 1 ? 100 : (userInfo.level == 2 ? 150 : (userInfo.level == 3 ? 200 : (userInfo.level == 4 ? 250 : (userInfo.level == 5 ? 300 : 'MAX'))))))}}</text>
                </view>
                <t-progress 
                  class="{{userInfo.level == 6 ? 'level-6' : ''}}"
                  percentage="{{expPercentage}}" 
                  color="#2ba471"
                  track-color="rgba(0, 0, 0, 0.1)"
                  stroke-width="12"
                  label="{{true}}"
                  theme="plump"
                ></t-progress>
              </view> -->
            </view>
          </view>
        </view>
        
        <!-- 添加编辑图标 -->
        <view class="edit-icon" wx:if="{{isLogined}}" bind:tap="showEditProfileModal">
          <text class="edit-text">编辑资料</text>
          <t-icon name="edit" size="48rpx" color="#2ba471" />
        </view>
      </view>
      
      <!-- 数据统计栏 -->
      <view class="stats-container" data-slogan="「绿意盎然 · 美好生活」">
        <view class="stat-item">
          <view class="stat-value">{{userInfo.collectionCount || 0}}</view>
          <view class="stat-label">收藏</view>
        </view>
       
      
        <view class="stat-item">
          <view class="stat-value">{{userInfo.reward}}</view>
          <view class="stat-label">积分</view>
        </view>
        
        <!-- 添加签到按钮到stats-container -->
        <view class="stat-item sign-in-item" wx:if="{{isLogined}}">
          <daily-check-in userId="{{userId}}" isLogined="{{isLogined}}" bind:signInSuccess="onSignInSuccess"></daily-check-in>
        </view>
      </view>
    
    
    <view class="content-area {{contentAnimationActive ? 'content-area-active' : ''}}">
      <!-- 我的订单 -->
      <view class="section-card">
        <view class="section-header">
          <view class="section-title">我的信息</view>
        </view>
        <view class="order-icons">
          <view class="order-icon-item" bindtap="navigateToMySupply">
            <view class="icon-wrapper">
              <t-icon name="terminal-window-filled" size="48rpx" color="#2ba471" />
            </view>
            <text>我的供应</text>
          </view>
          <view class="order-icon-item" bindtap="navigateToMyDemand">
            <view class="icon-wrapper">
              <t-icon name="assignment-filled" size="48rpx" color="#2ba471" />
              <!-- 新回价红点 -->
              <view class="red-dot" wx:if="{{hasNewDemandReplies}}"></view>
            </view>
            <text>我的求购</text>
          </view>
          <view class="order-icon-item" bindtap="navigateToMyQuote">
            <view class="icon-wrapper">
              <t-icon name="chart-bubble" size="48rpx" color="#2ba471" />
            </view>
            <text>我的报价</text>
          </view>

          <view class="order-icon-item" bindtap="navigateToMyCollection">
            <view class="icon-wrapper">
              <t-icon name="heart" size="48rpx" color="#2ba471" />
            </view>
            <text>收藏夹</text>
          </view>
          <view class="order-icon-item" bindtap="navigateToCallRecords">
            <view class="icon-wrapper">
              <t-icon name="call" size="48rpx" color="#2ba471" />
            </view>
            <text>拨号记录</text>
          </view>
          <view class="order-icon-item" bindtap="navigateToNavigationRecords">
            <view class="icon-wrapper">
              <t-icon name="location" size="48rpx" color="#2ba471" />
            </view>
            <text>导航记录</text>
          </view>
          <view class="order-icon-item admin-item" bindtap="navigateToQuoteAdmin" wx:if="{{userInfo.adm}}">
            <view class="icon-wrapper admin-icon">
              <t-icon name="tools" size="48rpx" color="#ffffff" />
            </view>
            <text class="admin-text">处理报价</text>
          </view>
          <!-- 添加导入植物数据按钮 -->
          <!-- <view class="order-icon-item" bindtap="importPlantsData">
            <view class="icon-wrapper">
              <t-icon name="upload" size="48rpx" color="#2ba471" />
            </view>
            <text>导入植物数据</text>
          </view>  -->
          <!-- <view class="order-icon-item">
            <view class="icon-wrapper">
              <t-icon name="deliver" size="48rpx" color="#2ba471" />
            </view>
            <text>待收货</text>
          </view>
          <view class="order-icon-item">
            <view class="icon-wrapper">
              <t-icon name="comment" size="48rpx" color="#2ba471" />
            </view>
            <text>待评价</text>
          </view> -->
          <!-- <view class="order-icon-item">
            <view class="icon-wrapper">
              <t-icon name="exchange" size="48rpx" color="#2ba471" />
            </view>
            <text>退换/售后</text>
          </view> -->
        </view>
      </view>

      <!-- 服务中心 -->
      <view class="section-card">
        <view class="section-header">
          <view class="section-title">服务中心</view>
        </view>
        
        <view class="service-icons">
          <!-- <view class="order-icon-item" bindtap="showRechargeModal">
            <view class="icon-wrapper">
              <t-icon name="wallet" size="48rpx" color="#2ba471" />
            </view>
            <text>积分充值</text>
          </view> -->

          <!-- Node.js后端积分充值 -->
          <view class="order-icon-item" bindtap="showNodePaymentModal">
            <view class="icon-wrapper">
              <t-icon name="wallet" size="48rpx" color="#2ba471" />
            </view>
            <text>积分充值</text>
          </view>

          <view class="order-icon-item" bindtap="callCustomerService">
            <view class="icon-wrapper">
              <t-icon name="service" size="48rpx" color="#2ba471" />
            </view>
            <text>联系客服</text>
          </view>

          <view class="order-icon-item" bindtap="showAboutUsModal">
            <view class="icon-wrapper">
              <t-icon name="info-circle" size="48rpx" color="#2ba471" />
            </view>
            <text>关于我们</text>
          </view>
          <!-- <view class="order-icon-item">
            <view class="icon-wrapper">
              <t-icon name="setting" size="48rpx" color="#2ba471" />
            </view>
            <text>设置</text>
          </view> -->
        </view>
      </view>

      <!-- 每日签到组件 -->
      <!-- 已移动到stats-container中 -->

      <!-- 植物养护小贴士 -->
      <view class="section-card">
        <view class="section-header">
          <view class="section-title">养护小贴士</view>
          <view class="tip-weekday">{{currentWeekday || '今日提示'}}</view>
        </view>
        <view class="tips-box">
          <view class="tip-item">
            <view class="tip-content">
              <view class="tip-title">{{currentTip.title || '养护小贴士'}}</view>
              <view class="tip-desc">{{currentTip.desc || '每天为您提供不同的养护知识'}}</view>
            </view>
            <view class="tip-icon" >
              <t-icon wx:if="{{currentTip.icon}}" name="{{currentTip.icon}}" size="64rpx" color="#75f1ad" />
              <t-icon wx:else name="help-circle" size="64rpx" color="#75f1ad" />
            </view>
          </view>
        </view>
      </view>
      
      <!--经验值卡片  -->
     
      
      <!-- <view class="section-card" wx:if="{{isLogined}}">
        <view class="section-header">
          <view class="section-title">经验值</view>
        </view>
        <view class="exp-content">
          <view class="exp-info">
            <text class="exp-level">当前等级: LV.{{userInfo.level || 0}}</text>
            <text class="exp-value">经验值: {{userInfo.exp || 0}}/{{userInfo.level >= 6 ? 'MAX' : (userInfo.level == 0 ? 50 : (userInfo.level == 1 ? 100 : (userInfo.level == 2 ? 150 : (userInfo.level == 3 ? 200 : (userInfo.level == 4 ? 250 : (userInfo.level == 5 ? 300 : 'MAX'))))))}}</text>
          </view>
          <view class="exp-progress-container">
            <t-progress 
              class="{{userInfo.level == 6 ? 'level-6' : ''}}"
              percentage="{{expPercentage}}" 
              color="#2ba471"
              track-color="rgba(0, 0, 0, 0.1)"
              stroke-width="12"
              label="{{true}}"
              theme="plump"
            ></t-progress>
          </view> -->
          
          <!-- 当达到最高等级时显示 -->
          <!-- <view class="exp-max-level" wx:if="{{userInfo.level >= 6}}">
            <view class="exp-max-text">已达到最高等级，无法获取更多经验</view>
          </view>
        </view>
      </view> -->

      <!-- 如果已登录，显示退出登录按钮 -->
      <view class="logout-section" wx:if="{{isLogined}}">
        <t-button theme="danger" size="large" bind:tap="logout" block>退出登录</t-button>
      </view>
      
      <!-- 底部安全区域，避免被tabbar遮挡 -->
      <view class="bottom-safe-area"></view>
    </view>
  </scroll-view>
</view>

<!-- 编辑个人资料模态框 -->
<t-popup visible="{{showEditModal}}" bind:visible-change="onEditModalClose" placement="center">
  <view class="edit-profile-modal">
    <view class="modal-header">
      <text class="modal-title">编辑个人资料</text>
      <t-icon name="close" size="48rpx" color="#666" bind:tap="onEditModalClose" />
    </view>
    
    <view class="modal-body">
      <!-- 微信头像选择器 -->
      <view class="wx-avatar-chooser">
        <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
          <block wx:if="{{avatarUrl || tempUserInfo.avatarUrl || userInfo.avatarUrl}}">
            <image class="avatar" src="{{avatarUrl || tempUserInfo.avatarUrl || userInfo.avatarUrl}}" mode="aspectFill"></image>
          </block>
          <block wx:else>
            <view class="default-avatar">
              <t-icon name="user" size="80rpx" color="#999999" />
            </view>
          </block>
        
        </button>
        <view class="avatar-tip">👆点击获取微信头像👆</view>
      </view>

      <!-- 微信昵称输入框 -->
      <view class="nickname-area">
        <view class="nickname-header">
          <text class="nickname-label">昵称</text>
          <text class="nickname-required">*必填</text>
        </view>
        <input 
          type="nickname" 
          class="nickname-input" 
          placeholder="请输入昵称" 
          value="{{tempUserInfo.nickName}}"
          bind:input="onInputNickname"
          maxlength="15"
        />
        <view class="nickname-tips">昵称长度1-15个字符，不能为空</view>
      </view>
      
      <!-- 手机号编辑区域 -->
      <view class="phone-area">
        <view class="phone-header">
          <text class="phone-label">手机号</text>
          <text class="nickname-required">*必填</text>
          <text class="phone-number-display" wx:if="{{tempUserInfo.phoneNumber}}">{{tempUserInfo.phoneNumber}}</text>
        </view>
        <view class="phone-input-container phone-input-stacked">
          <button class="get-phone-btn get-phone-btn-full" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">自动获取手机号</button>
        </view>
        <view class="phone-tips">手机号必须设置，点击按钮自动获取</view>
      </view>
    </view>
    
          <view class="modal-footer">
      <t-button style="background-color: #43a047 !important; color: white !important; border: none !important; box-shadow: 0 4rpx 10rpx rgba(67, 160, 71, 0.2) !important;" theme="light" size="large" class="cancel-btn" bind:tap="onEditModalClose">取消</t-button>
      <t-button style="background-color: #43a047 !important; border: none !important; box-shadow: 0 4rpx 10rpx rgba(67, 160, 71, 0.2) !important;" theme="primary" size="large" class="save-btn" bind:tap="saveProfileChanges" disabled="{{phoneNumberIsEmpty || !phoneNumberMatch}}">保存</t-button>
    </view>
  </view>
</t-popup>

<!--view class -->




<!-- 加载中提示 -->
<t-toast id="t-toast" />

<!-- 关于我们弹窗 -->
<t-popup visible="{{showAboutUsModal}}" bind:visible-change="onAboutUsModalClose" placement="center" prevent-scroll-through="{{false}}" overlay-props="{{ {preventScrollThrough: false} }}">
  <view class="about-us-modal">
    <view class="modal-header">
      <text class="modal-title">关于我们</text>
      <t-icon name="close" size="48rpx" color="#666" bind:tap="onAboutUsModalClose" />
    </view>
    
    <view class="modal-body">
      <view class="about-us-logo">
        <image src="https://6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1361723888.tcb.qcloud.la/static/LOG_withoutBackground.png?sign=ad4a944b6fdd50a955a6da3e49379f5f&t=1751186170" mode="aspectFit"></image>
      </view>
      <view class="about-us-title">成都苗木中心</view>
      <view class="about-us-slogan">「绿意盎然 · 美好生活」</view>
      
      <view class="about-us-description">
        <text>成都苗木中心是一个专注于苗木交易的平台，为用户提供供应发布、求购信息、退林还耕、苗圃转让和工程报价等多种服务的综合平台。</text>
      </view>
      
      <!-- <view class="about-us-features">
        <view class="feature-item">
          <view class="feature-icon">
            <t-icon name="shop" size="48rpx" color="#2ba471" />
          </view>
          <view class="feature-text">
            <text class="feature-title">供应中心</text>
            <text class="feature-desc">发布优质绿植产品</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <t-icon name="cart" size="48rpx" color="#2ba471" />
          </view>
          <view class="feature-text">
            <text class="feature-title">求购中心</text>
            <text class="feature-desc">发布求购需求</text>
          </view>
        </view>
        
        <view class="feature-item">
          <view class="feature-icon">
            <t-icon name="root-list" size="48rpx" color="#2ba471" />
          </view>
          <view class="feature-text">
            <text class="feature-title">工程报价</text>
            <text class="feature-desc">人工报价，给你更精确的报价服务</text>
          </view>
        </view>

        <view class="feature-item">
          <view class="feature-icon">
            <t-icon name="root-list" size="48rpx" color="#2ba471" />
          </view>
          <view class="feature-text">
            <text class="feature-title">苗圃转让</text>
            <text class="feature-desc">转让苗圃</text>
          </view>
        </view>


        <view class="feature-item">
          <view class="feature-icon">
            <t-icon name="root-list" size="48rpx" color="#2ba471" />
          </view>
          <view class="feature-text">
            <text class="feature-title">退林还耕</text>
            <text class="feature-desc">快速处理苗圃中的苗木</text>
          </view>
        </view>



      </view> -->
      
      <view class="about-us-contact">
        <text>客服电话：18384104768</text>
        <text>蜀ICP备2025145021号-1X</text>
      </view>
      
 
    </view>
    
    <view class="modal-footer">
      <t-button style="background-color: #43a047 !important; color: white !important; border: none !important; box-shadow: 0 4rpx 10rpx rgba(67, 160, 71, 0.2) !important;" theme="primary" size="large" class="confirm-btn" bind:tap="onAboutUsModalClose">确定</t-button>
    </view>
  </view>
</t-popup>

<!-- 充值弹窗 -->
<t-popup visible="{{showRechargeModal}}" bind:visible-change="onRechargeModalChange" placement="center" close-on-overlay-click="{{true}}" prevent-scroll-through="{{false}}">
  <view class="modal-container recharge-modal">
    <view class="modal-header">
      <view class="modal-title">积分充值</view>
      <view class="modal-close" bind:tap="hideRechargeModal">
        <t-icon name="close" size="48rpx" color="#999999" />
      </view>
    </view>

    <view class="modal-body">
      <!-- 当前余额显示 -->
      <view class="current-balance">
        <view class="balance-label">当前余额</view>
        <view class="balance-value">{{userInfo.reward}} 积分</view>
      </view>

      <!-- 充值金额选择 -->
      <view class="recharge-options">
        <view class="recharge-title">选择充值金额</view>
        <view class="recharge-amounts">
          <view
            class="amount-item {{selectedAmount === 0.01 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="0.01">
            <view class="amount-value">0.01元</view>
            <view class="amount-points">+1积分</view>
            <view class="test-label">测试</view>
          </view>
          <view
            class="amount-item {{selectedAmount === 10 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="10">
            <view class="amount-value">10元</view>
            <view class="amount-points">+10积分</view>
          </view>

          <view
            class="amount-item {{selectedAmount === 30 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="30">
            <view class="amount-value">30元</view>
            <view class="amount-points">+30积分</view>
          </view>

          <view
            class="amount-item {{selectedAmount === 50 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="50">
            <view class="amount-value">50元</view>
            <view class="amount-points">+50积分</view>
          </view>
          <view
            class="amount-item {{selectedAmount === 100 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="100">
            <view class="amount-value">100元</view>
            <view class="amount-points">+100积分</view>
          </view>
          <view
            class="amount-item {{selectedAmount === 500 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="500">
            <view class="amount-value">500元</view>
            <view class="amount-points">+500积分</view>
          </view>
          <view
            class="amount-item {{selectedAmount === 1000 ? 'selected' : ''}}"
            bind:tap="selectAmount"
            data-amount="1000">
            <view class="amount-value">1000元</view>
            <view class="amount-points">+1000积分</view>
          </view>
        </view>
      </view>

      <!-- 充值说明 -->
      <view class="recharge-notice">
        <view class="notice-title">充值说明</view>
        <view class="notice-content">
          <text>• 1元 = 1积分</text>
          <text>• 积分可用于平台各项服务</text>
          <text>• 充值后积分立即到账</text>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <t-button
        theme="default"
        size="large"
        bind:tap="hideRechargeModal"
        class="cancel-btn">
        取消
      </t-button>
      <t-button
        theme="primary"
        size="large"
        bind:tap="confirmRecharge"
        disabled="{{!selectedAmount}}"
        class="confirm-btn">
        确认充值
      </t-button>
    </view>
  </view>
</t-popup>

<!-- Node.js支付测试弹窗 -->
<t-popup visible="{{showNodePaymentModal}}" bind:visible-change="onNodePaymentModalChange" placement="center" close-on-overlay-click="{{true}}" prevent-scroll-through="{{false}}">
  <view class="modal-container recharge-modal">
    <view class="modal-header">
      <view class="modal-title">积分充值</view>
      <view class="modal-close" bind:tap="hideNodePaymentModal">
        <t-icon name="close" size="48rpx" color="#999999" />
      </view>
    </view>

    <view class="modal-body">
      <!-- 当前余额显示 -->
      <view class="current-balance">
        <view class="balance-label">当前余额</view>
        <view class="balance-value">{{userInfo.reward}} 积分</view>
      </view>

      <!-- 充值金额选择 -->
      <view class="recharge-options">
        <view class="recharge-title">选择充值金额</view>
        <view class="recharge-amounts">
          <view
            class="amount-item {{selectedNodeAmount === 0.01 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="0.01">
            <view class="amount-value">0.01元</view>
            <view class="amount-points">+1积分</view>
            <view class="test-label">测试</view>
          </view>
          <view
            class="amount-item {{selectedNodeAmount === 10 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="10">
            <view class="amount-value">10元</view>
            <view class="amount-points">+10积分</view>
          </view>

          <view
            class="amount-item {{selectedNodeAmount === 30 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="30">
            <view class="amount-value">30元</view>
            <view class="amount-points">+30积分</view>
          </view>

          <view
            class="amount-item {{selectedNodeAmount === 50 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="50">
            <view class="amount-value">50元</view>
            <view class="amount-points">+50积分</view>
          </view>

          <view
            class="amount-item {{selectedNodeAmount === 100 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="100">
            <view class="amount-value">100元</view>
            <view class="amount-points">+100积分</view>
          </view>

          <view
            class="amount-item {{selectedNodeAmount === 500 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="500">
            <view class="amount-value">500元</view>
            <view class="amount-points">+500积分</view>
          </view>

          <view
            class="amount-item {{selectedNodeAmount === 1000 ? 'selected' : ''}}"
            bind:tap="selectNodeAmount"
            data-amount="1000">
            <view class="amount-value">1000元</view>
            <view class="amount-points">+1000积分</view>
          </view>
        </view>
      </view>

      <!-- 充值说明 -->
      <view class="recharge-notice">
        <view class="notice-title">充值说明</view>
        <view class="notice-content">
          <text>• 1元 = 1积分</text>
          <text>• 积分可用于平台各项服务</text>
          <text>• 充值后积分立即到账</text>
          <text>• 使用独立Node.js服务器处理</text>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <t-button
        theme="default"
        size="large"
        bind:tap="hideNodePaymentModal"
        class="cancel-btn">
        取消
      </t-button>
      <t-button
        theme="primary"
        size="large"
        bind:tap="confirmNodePayment"
        disabled="{{!selectedNodeAmount}}"
        class="confirm-btn">
        确认支付
      </t-button>
    </view>
  </view>
</t-popup>
