const express = require('express');
const router = express.Router();
const wechatPayService = require('../services/wechatPayService');
const databaseService = require('../services/databaseService');
const { authenticateToken } = require('../middleware/auth');
const { 
  validateCreatePayment, 
  validateQueryPayment, 
  validateVerifyPayment 
} = require('../middleware/validation');
const { decryptWechatPayCallback } = require('../utils/crypto');
const config = require('../config/config');
const logger = require('../utils/logger');

/**
 * 创建支付订单
 * POST /api/payment/create
 */
router.post('/create', authenticateToken, validateCreatePayment, async (req, res) => {
  try {
    const { amount, points, description } = req.body;
    const { openid } = req.user;

    logger.info('Creating payment order:', { 
      amount, 
      points, 
      openid: openid?.substring(0, 10) + '...' 
    });

    // 创建支付订单
    const paymentResult = await wechatPayService.createPaymentOrder({
      amount,
      points,
      description,
      openid
    });

    if (!paymentResult.success) {
      return res.status(400).json({
        success: false,
        message: 'Failed to create payment order',
        error: paymentResult.error
      });
    }

    // 创建积分日志记录
    await databaseService.createPointLog({
      orderNo: paymentResult.out_trade_no,
      openid: openid,
      points: points,
      status: 'processing'
    });

    res.json({
      success: true,
      message: 'Payment order created successfully',
      data: paymentResult
    });

  } catch (error) {
    logger.error('Failed to create payment order:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment order',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * 查询支付订单状态
 * GET /api/payment/query/:orderNo
 */
router.get('/query/:orderNo', authenticateToken, validateQueryPayment, async (req, res) => {
  try {
    const { orderNo } = req.params;

    logger.info('Querying payment order:', { orderNo });

    const queryResult = await wechatPayService.queryPaymentOrder(orderNo);

    res.json({
      success: true,
      message: 'Payment order queried successfully',
      data: queryResult.data
    });

  } catch (error) {
    logger.error('Failed to query payment order:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to query payment order',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * 验证支付结果
 * POST /api/payment/verify
 */
router.post('/verify', authenticateToken, validateVerifyPayment, async (req, res) => {
  try {
    const { orderNo, expectedPoints } = req.body;
    const { openid } = req.user;

    logger.info('Verifying payment result:', { 
      orderNo, 
      expectedPoints,
      openid: openid?.substring(0, 10) + '...'
    });

    // 查询支付订单状态
    const queryResult = await wechatPayService.queryPaymentOrder(orderNo);

    logger.info('Payment query result:', {
      orderNo,
      trade_state: queryResult.data.trade_state,
      transaction_id: queryResult.data.transaction_id
    });

    if (queryResult.data.trade_state === 'SUCCESS') {
      logger.info('Payment status is SUCCESS, proceeding with points update');
      // 支付成功，检查订单是否已经处理过
      const orderExists = await databaseService.checkOrderExists(orderNo);

      logger.info('Order existence check result:', {
        orderNo,
        orderExists
      });

      if (orderExists) {
        logger.info('Order exists, proceeding with points update');

        // 先查询用户当前积分（支付前）
        logger.info('Querying user current points before update:', {
          openid: openid?.substring(0, 10) + '...'
        });

        const userBeforeUpdate = await databaseService.getUserByOpenid(openid);
        const pointsBeforeUpdate = userBeforeUpdate.data?.reward || 0;

        logger.info('User points before update:', {
          openid: openid?.substring(0, 10) + '...',
          currentPoints: pointsBeforeUpdate
        });

        // 更新用户积分
        logger.info('Payment successful, updating user points:', {
          orderNo,
          expectedPoints,
          pointsBeforeUpdate,
          openid: openid?.substring(0, 10) + '...'
        });

        const updateResult = await databaseService.updateUserPoints(openid, expectedPoints);

        if (updateResult.success) {
          // 更新积分日志状态为已完成
          await databaseService.updatePointLogStatus(orderNo, 'completed', queryResult.data.transaction_id);

          logger.info('Points updated successfully:', {
            orderNo,
            oldPoints: updateResult.data.oldPoints,
            addedPoints: updateResult.data.addedPoints,
            newPoints: updateResult.data.newPoints
          });

          // 再次查询用户积分验证更新结果
          logger.info('Verifying points update by querying user again:', {
            openid: openid?.substring(0, 10) + '...'
          });

          const userAfterUpdate = await databaseService.getUserByOpenid(openid);
          const pointsAfterUpdate = userAfterUpdate.data?.reward || 0;

          logger.info('User points after update verification:', {
            openid: openid?.substring(0, 10) + '...',
            pointsAfterUpdate,
            expectedPoints: updateResult.data.newPoints,
            updateSuccessful: pointsAfterUpdate === updateResult.data.newPoints
          });

          res.json({
            success: true,
            message: 'Payment verification completed and points updated',
            data: {
              paymentStatus: 'SUCCESS',
              orderNo: orderNo,
              oldPoints: updateResult.data.oldPoints,
              addedPoints: updateResult.data.addedPoints,
              newPoints: updateResult.data.newPoints,
              actualPointsInDB: pointsAfterUpdate,
              verified: true
            }
          });
        } else {
          throw new Error('Failed to update user points');
        }
      } else {
        // 订单不存在
        logger.warn('Order not found in database:', { orderNo });
        res.json({
          success: false,
          message: 'Order not found',
          data: {
            paymentStatus: 'SUCCESS',
            orderNo: orderNo,
            verified: false
          }
        });
      }
    } else {
      logger.info('Payment not completed yet:', {
        orderNo,
        trade_state: queryResult.data.trade_state
      });
      res.json({
        success: true,
        message: 'Payment not completed yet',
        data: {
          paymentStatus: queryResult.data.trade_state,
          orderNo: orderNo,
          verified: false
        }
      });
    }

  } catch (error) {
    logger.error('Failed to verify payment result:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify payment result',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * 微信支付回调
 * POST /api/payment/notify
 */
router.post('/notify', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const body = req.body.toString();
    const headers = req.headers;

    logger.info('Received WeChat payment callback');

    // 验证签名
    const signature = headers['wechatpay-signature'];
    const timestamp = headers['wechatpay-timestamp'];
    const nonce = headers['wechatpay-nonce'];

    // TODO: 实现签名验证
    // const isValidSignature = verifyWechatPaySignature(timestamp, nonce, body, signature, publicKey);
    // if (!isValidSignature) {
    //   return res.status(401).json({ code: 'FAIL', message: '签名验证失败' });
    // }

    // 解析回调数据
    const callbackData = JSON.parse(body);

    if (callbackData.event_type === 'TRANSACTION.SUCCESS') {
      // 解密支付数据
      const resource = callbackData.resource;
      const orderData = decryptWechatPayCallback(
        resource.ciphertext,
        resource.associated_data,
        resource.nonce,
        config.wechatPay.apiv3_key
      );

      // 处理支付成功
      await handlePaymentSuccess(orderData);

      res.json({ code: 'SUCCESS', message: '处理成功' });
    } else {
      res.json({ code: 'SUCCESS', message: '忽略事件' });
    }

  } catch (error) {
    logger.error('Failed to handle payment callback:', error);
    res.status(500).json({ code: 'FAIL', message: '处理失败' });
  }
});

/**
 * 处理支付成功逻辑
 * @param {object} orderData 订单数据
 */
async function handlePaymentSuccess(orderData) {
  const { out_trade_no, transaction_id, payer, amount } = orderData;

  try {
    // 计算积分
    const amountInYuan = amount.total / 100;
    const points = amountInYuan === 0.01 ? 1 : Math.floor(amountInYuan);
    const openid = payer.openid;

    logger.info('Processing payment success:', { 
      out_trade_no, 
      points,
      openid: openid?.substring(0, 10) + '...'
    });

    // 检查订单是否已处理
    const orderExists = await databaseService.checkOrderExists(out_trade_no);
    if (orderExists) {
      logger.warn('Order already processed:', { out_trade_no });
      return;
    }

    // 更新用户积分
    await databaseService.updateUserPoints(openid, points);

    // 更新积分日志状态
    await databaseService.updatePointLogStatus(out_trade_no, 'completed', transaction_id);

    logger.info('Payment success processed:', { out_trade_no, points });

  } catch (error) {
    logger.error('Failed to process payment success:', error);
    throw error;
  }
}

module.exports = router;
