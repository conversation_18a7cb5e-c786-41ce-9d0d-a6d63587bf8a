# ==================== 服务器配置 ====================
# 服务端口
PORT=3001

# 运行环境 (development, production, test)
NODE_ENV=production

# 服务器域名 (生产环境使用)
SERVER_DOMAIN=************

# ==================== 微信小程序配置 ====================
# 微信小程序AppID (请替换为您的实际AppID)
WECHAT_APPID=your_wechat_appid_here

# 微信小程序Secret (请替换为您的实际Secret)
WECHAT_SECRET=your_wechat_secret_here

# ==================== 微信支付配置 ====================
# 微信支付AppID (通常与小程序AppID相同)
WECHAT_PAY_APPID=your_wechat_pay_appid_here

# 微信支付商户号 (请替换为您的实际商户号)
WECHAT_PAY_MCHID=your_merchant_id_here

# 微信支付证书序列号 (请替换为您的实际证书序列号)
WECHAT_PAY_SERIAL_NO=your_certificate_serial_no_here

# 微信支付APIv3密钥 (请替换为您的实际APIv3密钥)
WECHAT_PAY_APIV3_KEY=your_apiv3_key_here

# 微信支付私钥配置说明：
# 方式1（推荐）：将 apiclient_key.pem 文件上传到项目根目录
# 方式2（备选）：在下面配置私钥内容
# WECHAT_PAY_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
# your_private_key_content_here
# -----END PRIVATE KEY-----"

# 支付回调地址 (需要替换为实际的服务器地址)
PAYMENT_NOTIFY_URL=http://************:3001/api/payment/notify

# JWT密钥
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production

# 数据库配置 (如果使用独立数据库)
# DATABASE_URL=mongodb://localhost:27017/payment_db

# 微信云开发配置
CLOUD_ENV_ID=miaomuzhongxin-0giu90bpa4cbeaf5
ENABLE_CLOUD_DATABASE=true

# 云开发HTTP API配置 (用于服务端调用)
# 获取方式：微信公众平台 -> 开发 -> 开发设置 -> 服务器配置
WECHAT_CLOUD_ACCESS_TOKEN_URL=https://api.weixin.qq.com/cgi-bin/token
WECHAT_CLOUD_DATABASE_URL=https://api.weixin.qq.com/tcb

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/payment-server.log

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
