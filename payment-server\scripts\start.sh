#!/bin/bash

# Payment Server 启动脚本

echo "Starting Payment Server..."

# 检查Node.js版本
echo "Checking Node.js version..."
node_version=$(node -v)
echo "Node.js version: $node_version"

# 检查npm版本
echo "Checking npm version..."
npm_version=$(npm -v)
echo "npm version: $npm_version"

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "Warning: .env file not found. Please copy .env.example to .env and configure it."
    if [ -f ".env.example" ]; then
        echo "Copying .env.example to .env..."
        cp .env.example .env
        echo "Please edit .env file with your actual configuration before starting the server."
        exit 1
    fi
fi

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# 创建日志目录
if [ ! -d "logs" ]; then
    echo "Creating logs directory..."
    mkdir -p logs
fi

# 检查PM2是否已安装
if ! command -v pm2 &> /dev/null; then
    echo "PM2 not found. Installing PM2 globally..."
    npm install -g pm2
fi

# 启动服务
echo "Starting Payment Server with PM2..."
npm run pm2:start

echo "Payment Server started successfully!"
echo "You can check the status with: pm2 status"
echo "View logs with: pm2 logs payment-server"
echo "Stop the server with: npm run pm2:stop"
