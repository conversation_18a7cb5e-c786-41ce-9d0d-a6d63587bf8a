require('dotenv').config();
const fs = require('fs');
const path = require('path');

// 读取私钥文件的函数
function loadPrivateKey() {
  try {
    // 私钥文件路径（优先级顺序）
    const keyPaths = [
      path.join(__dirname, '../../certs/apiclient_key.pem'),  // 推荐：certs目录
      path.join(__dirname, '../../apiclient_key.pem')        // 备选：根目录
    ];

    // 尝试从文件路径加载私钥
    for (const keyPath of keyPaths) {
      if (fs.existsSync(keyPath)) {
        console.log('Loading private key from file:', keyPath);
        const privateKey = fs.readFileSync(keyPath, 'utf8');
        console.log('Private key loaded successfully from file');

        // 验证私钥格式
        if (privateKey.includes('BEGIN PRIVATE KEY') && privateKey.includes('END PRIVATE KEY')) {
          return privateKey;
        } else {
          console.error('Invalid private key format in file:', keyPath);
        }
      }
    }

    // 如果文件都不存在，使用环境变量
    console.warn('Private key file not found in any of these locations:');
    keyPaths.forEach(path => console.warn(' -', path));
    console.warn('Falling back to environment variable WECHAT_PAY_PRIVATE_KEY');
    return process.env.WECHAT_PAY_PRIVATE_KEY;

  } catch (error) {
    console.error('Error loading private key file:', error.message);
    console.warn('Falling back to environment variable WECHAT_PAY_PRIVATE_KEY');
    return process.env.WECHAT_PAY_PRIVATE_KEY;
  }
}

const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development'
  },

  // 微信小程序配置
  wechat: {
    appid: process.env.WECHAT_APPID,
    secret: process.env.WECHAT_SECRET
  },

  // 微信支付配置
  wechatPay: {
    appid: process.env.WECHAT_PAY_APPID,
    mchid: process.env.WECHAT_PAY_MCHID,
    serial_no: process.env.WECHAT_PAY_SERIAL_NO,
    apiv3_key: process.env.WECHAT_PAY_APIV3_KEY,
    private_key: loadPrivateKey(), // 从文件读取私钥，如果文件不存在则使用环境变量
    notify_url: process.env.PAYMENT_NOTIFY_URL
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'default_jwt_secret',
    expiresIn: '24h'
  },

  // 云数据库配置
  cloud: {
    envId: process.env.CLOUD_ENV_ID
  },

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/payment-server.log'
  },

  // 云数据库配置
  cloudDatabase: {
    enabled: process.env.ENABLE_CLOUD_DATABASE === 'true',
    envId: process.env.CLOUD_ENV_ID || 'miaomuzhongxin-0giu90bpa4cbeaf5'
  },

  // 安全配置
  security: {
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000,
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
  }
};

// 验证必要的配置
const requiredConfigs = [
  'WECHAT_APPID',
  'WECHAT_PAY_APPID',
  'WECHAT_PAY_MCHID',
  'WECHAT_PAY_APIV3_KEY',
  'PAYMENT_NOTIFY_URL'
];

const missingConfigs = requiredConfigs.filter(key => !process.env[key]);

// 检查私钥是否可用（文件或环境变量）
if (!config.wechatPay.private_key) {
  console.error('Private key not found! Please either:');
  console.error('1. Upload apiclient_key.pem file to /www/wwwroot/payment-server/');
  console.error('2. Set WECHAT_PAY_PRIVATE_KEY environment variable');
  missingConfigs.push('WECHAT_PAY_PRIVATE_KEY (file or env var)');
}

if (missingConfigs.length > 0) {
  console.error('Missing required configurations:', missingConfigs);
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
}

module.exports = config;
