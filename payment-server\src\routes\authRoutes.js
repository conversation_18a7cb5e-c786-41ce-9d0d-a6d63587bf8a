const express = require('express');
const router = express.Router();
const wechatService = require('../services/wechatService');
const databaseService = require('../services/databaseService');
const { generateToken } = require('../middleware/auth');
const { validateWechatLogin } = require('../middleware/validation');
const logger = require('../utils/logger');

/**
 * 微信小程序登录
 * POST /api/auth/wechat-login
 */
router.post('/wechat-login', validateWechatLogin, async (req, res) => {
  try {
    const { code, userInfo } = req.body;

    // 通过code获取openid
    const wechatData = await wechatService.getOpenidByCode(code);
    const { openid, session_key } = wechatData;

    if (!openid) {
      return res.status(400).json({
        success: false,
        message: 'Failed to get user openid from WeChat'
      });
    }

    // 查询或创建用户
    let user = await databaseService.getUserByOpenid(openid);
    
    if (!user.success || !user.data) {
      // 用户不存在，创建新用户
      logger.info('Creating new user:', { openid: openid.substring(0, 10) + '...' });
      
      // TODO: 实现创建新用户的逻辑
      user = {
        success: true,
        data: {
          _id: 'new_user_id_' + Date.now(),
          openid: openid,
          nickName: userInfo?.nickName || 'New User',
          avatarUrl: userInfo?.avatarUrl || '',
          reward: 0,
          createTime: new Date()
        }
      };
    }

    // 生成JWT token
    const token = generateToken({
      userId: user.data._id,
      openid: openid,
      nickName: user.data.nickName
    });

    logger.info('User login successful:', { 
      userId: user.data._id,
      openid: openid.substring(0, 10) + '...'
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token: token,
        user: {
          id: user.data._id,
          nickName: user.data.nickName,
          avatarUrl: user.data.avatarUrl,
          reward: user.data.reward
        }
      }
    });

  } catch (error) {
    logger.error('WeChat login failed:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * 刷新token
 * POST /api/auth/refresh-token
 */
router.post('/refresh-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      });
    }

    // 验证旧token（即使过期也要能解析）
    const jwt = require('jsonwebtoken');
    const config = require('../config/config');
    
    let decoded;
    try {
      decoded = jwt.verify(token, config.jwt.secret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        decoded = jwt.decode(token);
      } else {
        throw error;
      }
    }

    if (!decoded || !decoded.openid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid token'
      });
    }

    // 生成新token
    const newToken = generateToken({
      userId: decoded.userId,
      openid: decoded.openid,
      nickName: decoded.nickName
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newToken
      }
    });

  } catch (error) {
    logger.error('Token refresh failed:', error);
    res.status(500).json({
      success: false,
      message: 'Token refresh failed'
    });
  }
});

/**
 * 验证token状态
 * GET /api/auth/verify
 */
router.get('/verify', require('../middleware/auth').authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Token is valid',
    data: {
      user: req.user
    }
  });
});

module.exports = router;
