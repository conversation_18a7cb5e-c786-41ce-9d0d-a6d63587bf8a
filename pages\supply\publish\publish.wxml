<!-- pages/supply/publish/publish.wxml -->
<page-meta>
  <navigation-bar title="发布供应" back="{{true}}" homeButton="{{true}}" extClass="custom-nav" hideTree="{{true}}" bindback="onNavBack"></navigation-bar>
</page-meta>
<view class="container">
  <view class="form-container">

    <!-- 图片上传 - 移到最上方 -->
    <view class="form-section card-shadow image-section">
      <view class="image-uploader {{errors.imageList ? 'error' : ''}}">
        <view class="image-list">
          <block wx:for="{{newImageList}}" wx:key="index">
            <view class="image-item">
              <image src="{{item.url}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
              <view class="delete-btn-wrapper" hover-class="delete-btn-wrapper-hover" catchtap="deleteImage" data-index="{{index}}">
                <view class="delete-btn">×</view>
              </view>
            </view>
          </block>

          <view class="upload-btn camera-btn" hover-class="upload-btn-hover" bindtap="chooseImage" wx:if="{{newImageList.length < 6}}">
            <view class="upload-icon">
              <t-icon name="camera-filled" size="42rpx" color="#07c160"></t-icon>
            </view>
            <view class="upload-text">拍照上传</view>
          </view>
        </view>
        <view class="image-tips">最多上传6张图片，请实地拍摄</view>
      </view>
      <view wx:if="{{errors.imageList}}" class="error-msg">{{errors.imageList}}</view>
    </view>

    <!-- 作物参数 -->
    <view class="form-section card-shadow">
      <!-- 苗木规格设置 -->
      <view class="sub-section-title" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20rpx;">
        <view>苗木规格<text class="highlight-text">（至少填写一个参数）</text></view>
        <!-- 切换按钮 -->
        <view class="toggle-button" hover-class="toggle-button-hover" bindtap="onSwitchToDemand">
          <view class="toggle-icon">↔</view>
          <view class="toggle-text">切换到求购</view>
        </view>
      </view>

      <view class="specs-grid">
        <!-- 植物名 -->
        <view class="spec-item required-spec full-width" id="title-input-section" bindtap="onTitleClick">
          <view class="form-label">供应名<text class="required">*</text></view>
          <view class="measure-input-container {{errors.title ? 'error' : ''}}">
            <input class="form-input" type="text" placeholder="请输入植物名称" placeholder-class="spec-placeholder" value="{{formData.title}}" bindinput="onInputChange" data-field="title" bindfocus="onTitleFocus" bindblur="onTitleBlur" adjust-position="{{false}}" />
          </view>
          <view wx:if="{{errors.title}}" class="error-msg">{{errors.title}}</view>

          <!-- 植物名称推荐列表 -->
          <view class="plant-suggestions-container" wx:if="{{showSuggestions && plantSuggestions.length > 0}}" catchtap="stopPropagation" catchtouchmove="stopPropagation" catchlongpress="stopPropagation">
            <view class="suggestions-header">
              <text class="suggestions-title">你想输入 (共{{plantSuggestions.length}}项)</text>
              <view class="suggestions-close" bindtap="closeSuggestions">×</view>
            </view>
            <scroll-view scroll-y class="suggestions-scroll" catchtouchmove="stopPropagation">
              <view class="suggestion-item" wx:for="{{plantSuggestions}}" wx:key="index" hover-class="suggestion-item-hover" catchtap="onSelectSuggestion" data-name="{{item.name}}">
                <view class="suggestion-name">{{item.name}}</view>
              </view>
            </scroll-view>
          </view>
        </view>

        <!-- 分类 - 暂时隐藏 -->
        <view class="spec-item" style="display: none;">
          <view class="form-label">分类</view>
          <picker mode="selector" range="{{categoryOptions}}" bindchange="onCategoryChange" style="width: 54%;">
            <view class="picker {{errors.category ? 'error' : ''}}" style="width: 100%;">
              <text>{{formData.category || '请选择'}}</text>
              <view class="picker-arrow"></view>
            </view>
          </picker>
          <view wx:if="{{errors.category}}" class="error-msg">{{errors.category}}</view>
        </view>

        <!-- 米径 - 藤本类、草皮类、花草和种子不显示 -->
        <view class="spec-item" wx:if="{{specVisibility.trunkDiameter && formData.category !== '藤本类' && formData.category !== '草皮类' && formData.category !== '花草' && formData.category !== '种子'}}">
          <view class="form-label">米径<text class="required" wx:if="{{requiredSpecs.trunkDiameter}}">*</text></view>
          <view class="measure-input-container {{errors.trunkDiameter ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="1米处量" placeholder-class="spec-placeholder" value="{{formData.trunkDiameter}}" bindinput="onInputChange" data-field="trunkDiameter" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.trunkDiameter}}" class="error-msg">{{errors.trunkDiameter}}</view>
        </view>




        <!-- 地径 - 藤本类和花草不显示 -->
        <view class="spec-item" wx:if="{{specVisibility.groundDiameter && formData.category !== '藤本类' && formData.category !== '花草'}}">
          <view class="form-label">地径<text class="required" wx:if="{{requiredSpecs.groundDiameter}}">*</text></view>
          <view class="measure-input-container {{errors.groundDiameter ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="20公分量" placeholder-class="spec-placeholder" value="{{formData.groundDiameter}}" bindinput="onInputChange" data-field="groundDiameter" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.groundDiameter}}" class="error-msg">{{errors.groundDiameter}}</view>
        </view>

        <!-- 分枝点 -->
        <view class="spec-item {{requiredSpecs.branchPoint ? 'required-spec' : ''}}" wx:if="{{specVisibility.branchPoint}}">
          <view class="form-label">分枝<text class="required" wx:if="{{requiredSpecs.branchPoint}}">*</text></view>
          <view class="measure-input-container {{errors.branchPoint ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="分枝处" placeholder-class="spec-placeholder" value="{{formData.branchPoint}}" bindinput="onInputChange" data-field="branchPoint" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.branchPoint}}" class="error-msg">{{errors.branchPoint}}</view>
        </view>


        <!-- 胸径 -->
        <view class="spec-item {{requiredSpecs.chestDiameter ? 'required-spec' : ''}}" wx:if="{{specVisibility.chestDiameter}}">
          <view class="form-label">胸径 <text class="required" wx:if="{{requiredSpecs.chestDiameter}}">*</text></view>
          <view class="measure-input-container {{errors.chestDiameter ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="1.2米量" placeholder-class="spec-placeholder" value="{{formData.chestDiameter}}" bindinput="onInputChange" data-field="chestDiameter" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.chestDiameter}}" class="error-msg">{{errors.chestDiameter}}</view>
        </view>

        <!-- 高度 - 藤本类、草皮类和种子不显示 -->
        <view class="spec-item required-spec" wx:if="{{specVisibility.height && formData.category !== '藤本类' && formData.category !== '草皮类' && formData.category !== '种子'}}">
          <view class="form-label">高度<text class="required" wx:if="{{requiredSpecs.height}}">*</text></view>
          <view class="measure-input-container {{errors.height ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入高度" placeholder-class="spec-placeholder" value="{{formData.height}}" bindinput="onInputChange" data-field="height" />
            <view class="unit-text">公分</view>
          </view>

          <view wx:if="{{errors.height}}" class="error-msg">{{errors.height}}</view>
        </view>



        <!-- 丛生 -->
        <view class="spec-item {{requiredSpecs.clumpCount ? 'required-spec' : ''}}" wx:if="{{specVisibility.clumpCount}}">
          <view class="form-label">丛生 <text class="required" wx:if="{{requiredSpecs.clumpCount}}">*</text></view>
          <view class="measure-input-container {{errors.clumpCount ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入丛生数量" placeholder-class="spec-placeholder" value="{{formData.clumpCount}}" bindinput="onInputChange" data-field="clumpCount" />
            <view class="unit-text">杆</view>
          </view>
          <view wx:if="{{errors.clumpCount}}" class="error-msg">{{errors.clumpCount}}</view>
        </view>




        <!-- 冠幅 - 藤本类不显示 -->
        <view class="spec-item  required-spec" wx:if="{{specVisibility.crownWidth && formData.category !== '藤本类'}}">
          <view class="form-label">冠幅<text class="required" wx:if="{{requiredSpecs.crownWidth}}">*</text></view>
          <view class="measure-input-container {{errors.crownWidth ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入冠幅" placeholder-class="spec-placeholder" value="{{formData.crownWidth}}" bindinput="onInputChange" data-field="crownWidth" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.crownWidth}}" class="error-msg">{{errors.crownWidth}}</view>
        </view>



        <!-- 主蔓长度 -->
        <view class="spec-item {{requiredSpecs.mainVineLength ? 'required-spec' : ''}}" wx:if="{{specVisibility.mainVineLength}}">
          <view class="form-label">主蔓长度 <text class="required" wx:if="{{requiredSpecs.mainVineLength}}">*</text></view>
          <view class="measure-input-container {{errors.mainVineLength ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="" placeholder-class="spec-placeholder" value="{{formData.mainVineLength}}" bindinput="onInputChange" data-field="mainVineLength" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.mainVineLength}}" class="error-msg">{{errors.mainVineLength}}</view>
        </view>

        <!-- 分支数 -->
        <view class="spec-item {{requiredSpecs.branchCount ? 'required-spec' : ''}}" wx:if="{{specVisibility.branchCount}}">
          <view class="form-label">分支数 <text class="required" wx:if="{{requiredSpecs.branchCount}}">*</text></view>
          <view class="measure-input-container {{errors.branchCount ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="分支数量" placeholder-class="spec-placeholder" value="{{formData.branchCount}}" bindinput="onInputChange" data-field="branchCount" />
            <view class="unit-text">支</view>
          </view>
          <view wx:if="{{errors.branchCount}}" class="error-msg">{{errors.branchCount}}</view>
        </view>

        <!-- 株/m² -->
        <view class="spec-item {{requiredSpecs.plantDensity ? 'required-spec' : ''}}" wx:if="{{specVisibility.plantDensity}}">
          <view class="form-label">密度 <text class="required" wx:if="{{requiredSpecs.plantDensity}}">*</text></view>
          <view class="measure-input-container {{errors.plantDensity ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入密度" placeholder-class="spec-placeholder" value="{{formData.plantDensity}}" bindinput="onInputChange" data-field="plantDensity" />
            <view class="unit-text">株/m²</view>
          </view>
          <view wx:if="{{errors.plantDensity}}" class="error-msg">{{errors.plantDensity}}</view>
        </view>





        <!-- 杆径 -->
        <view class="spec-item {{requiredSpecs.clumpDiameter ? 'required-spec' : ''}}" wx:if="{{specVisibility.clumpDiameter}}">
          <view class="form-label">杆径 <text class="required" wx:if="{{requiredSpecs.clumpDiameter}}">*</text></view>
          <view class="measure-input-container {{errors.clumpDiameter ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入杆径" placeholder-class="spec-placeholder" value="{{formData.clumpDiameter}}" bindinput="onInputChange" data-field="clumpDiameter" />
            <view class="unit-text">公分</view>
          </view>
          <view wx:if="{{errors.clumpDiameter}}" class="error-msg">{{errors.clumpDiameter}}</view>
        </view>




        <!-- 杯口 -->
        <view class="spec-item {{requiredSpecs.cupSize ? 'required-spec' : ''}}" wx:if="{{specVisibility.cupSize}}">
          <view class="form-label">杯口 <text class="required" wx:if="{{requiredSpecs.cupSize}}">*</text></view>
          <view class="measure-input-container {{errors.cupSize ? 'error' : ''}}">
            <input class="form-input" type="digit" placeholder="输入杯口" placeholder-class="spec-placeholder" value="{{formData.cupSize}}" bindinput="onInputChange" data-field="cupSize" />
            <view class="unit-text">杯</view>
          </view>
          <view wx:if="{{errors.cupSize}}" class="error-msg">{{errors.cupSize}}</view>
        </view>

        <!-- 苗龄 -->
        <view class="spec-item {{requiredSpecs.plantAge ? 'required-spec' : ''}}" wx:if="{{specVisibility.plantAge && formData.category !== '种子'}}">
          <view class="form-label">苗龄 <text class="required" wx:if="{{requiredSpecs.plantAge}}">*</text></view>
          <view class="measure-input-container {{errors.plantAge ? 'error' : ''}}">
            <input class="form-input" type="text" placeholder="请输入苗龄" placeholder-class="spec-placeholder" value="{{formData.plantAge}}" bindinput="onInputChange" data-field="plantAge" />
          </view>
          <view wx:if="{{errors.plantAge}}" class="error-msg">{{errors.plantAge}}</view>
        </view>
      </view>


    </view>

    <!-- 基本信息 -->
    <view class="form-section card-shadow">
      <!-- 标题 -->
      <view class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20rpx;">
        <view class="section-title">基本信息</view>
      </view>

      <!-- 两列式flex流式布局 -->
      <view style="display: flex; flex-wrap: wrap; width: 100%;">
        <!-- 第二行：上车价和地价 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx;">
          <!-- 上车价 -->
          <view style="display: flex; width: 50%; padding-right: 10rpx; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 2rpx 8rpx; border-left: 3rpx solid #ff9800;">上车价<text class="required">*</text></view>
            <view class="price-input-container {{errors.price ? 'error' : ''}}" style="flex: 1; padding: 0 5rpx;">
              <input class="form-input price-input" style="padding: 0 5rpx;" type="digit" placeholder="输入上车价" placeholder-style="font-size: 18rpx;" placeholder-class="placeholder" value="{{formData.price}}" bindinput="onInputChange" data-field="price" data-price-type="上车价" />
              <view class="unit-text">元</view>
            </view>
          </view>

          <!-- 地价 -->
          <!-- <view style="display: flex; width: 50%; padding-left: 10rpx; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx;">地价</view>
            <view class="price-input-container {{errors.localPrice ? 'error' : ''}}" style="flex: 1; padding: 0 5rpx;">
              <input class="form-input price-input" style="padding: 0 5rpx;" type="digit" placeholder="输入地价" placeholder-style="font-size: 18rpx;" placeholder-class="placeholder" value="{{formData.localPrice}}" bindinput="onInputChange" data-field="localPrice" data-price-type="地价" />
              <view class="unit-text">元</view>
            </view>
          </view> -->
        </view>

        <!-- 第三行：供应数量和栽培状态 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx;">
          <!-- 供应数量 -->
          <view style="display: flex; width: 50%; padding-right: 10rpx; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 2rpx 8rpx; border-left: 3rpx solid #ff9800;">供应数量<text class="required">*</text></view>
            <view class="quantity-input-container {{errors.quantity ? 'error' : ''}}" style="flex: 1; display: flex; align-items: center;">
              <input class="form-input" type="text" placeholder="输入数量" placeholder-class="placeholder" value="{{formData.quantity}}" bindinput="onInputChange" data-field="quantity" style="flex: 1;" />

              <!-- 价格单位选择器 -->
              <picker mode="selector" range="{{priceUnitOptions}}" bindchange="onPriceUnitChange" class="price-unit-picker" style="margin-left: 5rpx; height: 80%;">
                <view class="price-unit-text">
                  <text>{{formData.price_unit}}</text>
                  <view class="picker-arrow-small"></view>
                </view>
              </picker>
            </view>
          </view>

          <!-- 栽培状态 -->
          <view style="display: flex; width: 50%; padding-left: 10rpx; box-sizing: border-box;">
            <view class="form-label" style="min-width: 120rpx;">栽培状态</view>
            <view style="flex: 0.9;">
              <picker mode="selector" range="{{plantMethodOptions}}" bindchange="onPlantMethodChange" style="flex: 1; height: 64rpx;">
                <view class="area-picker-trigger {{errors.plant_method ? 'error' : ''}}" style="width: 100%; height: 64rpx;">
                  <text class="{{formData.plant_method ? '' : 'placeholder'}}">{{formData.plant_method}}</text>
                  <view class="picker-arrow"></view>
                </view>
              </picker>
            </view>
          </view>

        </view>

        <!-- 第四行：产品质量独占一行 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx; align-items: center;">
          <view class="form-label" style="min-width: 120rpx; background-color: rgba(255, 152, 0, 0.05); border-radius: 6rpx; color: #ff9800; padding: 2rpx 8rpx; border-left: 3rpx solid #ff9800; margin-right: 20rpx;">产品质量</view>
          <radio-group bindchange="onGrowthStageRadioChange" style="flex: 1; display: flex; align-items: center;">
            <label wx:for="{{growthStageOptions}}" wx:key="index" style="margin-right: 30rpx; display: flex; align-items: center; font-size: 26rpx;">
              <radio value="{{item}}" checked="{{formData.growthStage === item}}" style="margin-right: 10rpx; transform: scale(0.9);" />
              <text>{{item}}</text>
            </label>
          </radio-group>
        </view>

        <!-- 第五行：所在地区平铺整行 -->
        <view style="display: flex; width: 100%; margin-bottom: 15rpx; align-items: center;">
          <view class="form-label" style="min-width: 120rpx; margin-right: 20rpx;">所在地区</view>
          <view class="area-picker-trigger {{errors.location ? 'error' : ''}}" hover-class="area-picker-hover" bindtap="onAreaPicker" style="flex: 1; max-width: 500rpx;">
            <text>{{areaText || '可选择所在地区'}}</text>
            <view class="picker-arrow"></view>
          </view>
          <!-- 刷新按钮 - 仅在定位失败时显示 -->
          <view wx:if="{{locationFailed}}" class="location-refresh-btn" bindtap="onRefreshLocation">
            <view class="refresh-icon {{refreshing ? 'rotating' : ''}}">⟳</view>
          </view>
        </view>

        <!-- 定位失败提示 - 位于所在地区下方 -->
        <view wx:if="{{locationFailed}}" class="location-warning">
          <view class="warning-icon">⚠</view>
          <text class="warning-text">由于未打开定位服务，发布后，该位置无法被导航。可以打开定位再点击自动获取</text>
        </view>
      </view>

      <!-- 错误信息 -->
      <view style="display: flex; flex-wrap: wrap; width: 100%; margin-top: 5rpx;">
        <view wx:if="{{errors.category}}" class="error-msg" style="margin-right: 20rpx;">{{errors.category}}</view>
        <view wx:if="{{errors.price}}" class="error-msg" style="margin-right: 20rpx;">{{errors.price}}</view>
        <view wx:if="{{errors.quantity}}" class="error-msg" style="margin-right: 20rpx;">{{errors.quantity}}</view>
        <view wx:if="{{errors.location}}" class="error-msg" style="margin-right: 20rpx;">{{errors.location}}</view>
        <view wx:if="{{errors.growthStage}}" class="error-msg" style="margin-right: 20rpx;">{{errors.growthStage}}</view>
        <view wx:if="{{errors.plant_method}}" class="error-msg" style="margin-right: 20rpx;">{{errors.plant_method}}</view>
      </view>
    </view>

    <!-- 详细描述 -->
    <view class="form-section card-shadow description-section">
      <view class="section-header">
        <view class="section-title" style="position: relative; left: 0rpx; top: 14rpx">详细描述</view>
      </view>


      <textarea class="form-textarea {{errors.content ? 'error' : ''}}" placeholder="可选填，请详细描述您的供应信息，可以补充品种、数量、品质或交易信息" placeholder-class="textarea-placeholder" maxlength="500" value="{{formData.content}}" bindinput="onInputChange" data-field="content" />
      <view class="textarea-counter">{{formData.content.length || 0}}/500</view>
      <view wx:if="{{errors.content}}" class="error-msg">{{errors.content}}</view>
    </view>

    <!-- 联系信息 -->
    <view class="form-section card-shadow">
      <view class="section-header">
        <view class="section-title">联系方式</view>

      </view>

      <!-- 联系电话 - 改为水平布局 -->
      <view class="form-item horizontal">
        <view class="form-label">联系电话<text class="required">*</text></view>
        <input class="form-input {{errors.contactPhone ? 'error' : ''}}" type="number" placeholder="请输入联系电话" placeholder-class="placeholder" value="{{formData.contactPhone}}" bindinput="onInputChange" data-field="contactPhone" />
      </view>
      <view wx:if="{{errors.contactPhone}}" class="error-msg">{{errors.contactPhone}}</view>
    </view>








    <!-- 提交按钮 -->
    <view class="form-actions">
      <button class="btn btn-cancel" hover-class="btn-cancel-hover" bindtap="cancelPublish" disabled="{{submitting}}">取消</button>
      <button class="btn btn-submit" hover-class="btn-submit-hover" bindtap="submitForm" disabled="{{submitting}}" loading="{{submitting}}">发布供应</button>
    </view>
  </view>

  <!-- 继续发布提示弹窗 -->
  <view class="continue-publish-modal" wx:if="{{showContinueModal}}">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-title">发布成功！</view>
      <view class="modal-message">请返回供应页面查看！</view>
      <view class="modal-buttons">
        <button class="modal-btn cancel-btn" bindtap="cancelContinue">返回大厅</button>
        <button class="modal-btn confirm-btn" bindtap="confirmContinue">继续发布</button>
      </view>
    </view>
  </view>
</view>

<!-- 地区选择器 -->
<t-picker visible="{{areaVisible}}" value="{{areaValue}}" title="选择地区" cancelBtn="取消" confirmBtn="确认" bindchange="onPickerChange" bindpick="onColumnChange" bindcancel="onPickerCancel">
  <t-picker-item options="{{provinces}}"></t-picker-item>
  <t-picker-item options="{{cities}}"></t-picker-item>
</t-picker>

<!-- 隐藏的canvas用于绘制水印，使用Canvas 2D -->
<canvas type="2d" id="watermark-canvas" style="position: absolute; left: -9999px; width: 300px; height: 300px;"></canvas>