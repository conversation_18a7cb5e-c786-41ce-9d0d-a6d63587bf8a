# Payment Server

基于Node.js的微信支付服务后端

## 功能特性

- 微信小程序登录认证
- 微信支付订单创建
- 支付状态查询
- 支付回调处理
- 积分系统集成
- JWT身份验证
- 请求限流
- 日志记录

## 项目结构

```
payment-server/
├── src/
│   ├── app.js                 # 主应用入口
│   ├── config/
│   │   └── config.js          # 配置文件
│   ├── middleware/
│   │   ├── auth.js            # JWT认证中间件
│   │   └── validation.js      # 请求验证中间件
│   ├── routes/
│   │   ├── authRoutes.js      # 认证路由
│   │   └── paymentRoutes.js   # 支付路由
│   ├── services/
│   │   ├── wechatService.js   # 微信服务
│   │   ├── wechatPayService.js # 微信支付服务
│   │   └── databaseService.js # 数据库服务
│   └── utils/
│       ├── crypto.js          # 加密工具
│       └── logger.js          # 日志工具
├── logs/                      # 日志目录
├── .env.example              # 环境变量示例
├── ecosystem.config.js       # PM2配置
└── package.json
```

## 安装部署

### 1. 安装依赖

```bash
cd payment-server
npm install
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的配置信息
```

### 3. 本地开发

```bash
npm run dev
```

### 4. 生产部署

```bash
# 使用PM2部署
npm run pm2:start

# 或直接启动
npm start
```

## API接口

### 认证接口

#### 微信登录
```
POST /api/auth/wechat-login
Content-Type: application/json

{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```

#### 刷新Token
```
POST /api/auth/refresh-token
Content-Type: application/json

{
  "token": "旧的JWT token"
}
```

### 支付接口

#### 创建支付订单
```
POST /api/payment/create
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "amount": 1.0,
  "points": 1,
  "description": "积分充值"
}
```

#### 查询支付状态
```
GET /api/payment/query/:orderNo
Authorization: Bearer <JWT_TOKEN>
```

#### 验证支付结果
```
POST /api/payment/verify
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "orderNo": "订单号",
  "expectedPoints": 1
}
```

## 环境变量配置

| 变量名 | 说明 | 必需 |
|--------|------|------|
| PORT | 服务端口 | 否 |
| NODE_ENV | 运行环境 | 否 |
| WECHAT_APPID | 微信小程序AppID | 是 |
| WECHAT_SECRET | 微信小程序Secret | 是 |
| WECHAT_PAY_MCHID | 微信支付商户号 | 是 |
| WECHAT_PAY_APIV3_KEY | 微信支付APIv3密钥 | 是 |
| WECHAT_PAY_PRIVATE_KEY | 微信支付私钥 | 是 |
| PAYMENT_NOTIFY_URL | 支付回调地址 | 是 |
| JWT_SECRET | JWT密钥 | 否 |

## 安全注意事项

1. 确保所有敏感配置信息都通过环境变量设置
2. 生产环境必须使用HTTPS
3. 定期更新依赖包
4. 配置适当的防火墙规则
5. 启用请求限流
6. 定期备份日志和数据

## 监控和日志

- 日志文件位于 `logs/` 目录
- 支持不同级别的日志记录
- 生产环境建议配置日志轮转
- 可集成第三方监控服务

## 故障排除

### 常见问题

1. **微信支付回调失败**
   - 检查回调URL是否正确配置
   - 确认服务器可以接收HTTPS请求
   - 验证签名算法是否正确

2. **JWT认证失败**
   - 检查JWT_SECRET配置
   - 确认token格式正确
   - 检查token是否过期

3. **数据库连接问题**
   - 检查数据库配置
   - 确认网络连接正常
   - 验证数据库权限

## 开发说明

- 使用ESLint进行代码规范检查
- 遵循RESTful API设计原则
- 所有异步操作使用async/await
- 错误处理统一使用try/catch
- 重要操作都有详细日志记录
