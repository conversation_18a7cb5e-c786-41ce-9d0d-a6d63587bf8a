const tcb = require('@cloudbase/node-sdk');
const config = require('../config/config');
const logger = require('../utils/logger');

/**
 * 微信云开发数据库服务
 * 使用官方 @cloudbase/node-sdk
 */
class CloudDatabaseService {
  constructor() {
    // 初始化云开发SDK
    this.app = tcb.init({
      env: config.cloudDatabase.envId,
      secretId: 'AKIDqhYJsGSRCmCestjMp8KJ4kFYeWFl4C8s',
      secretKey: 'f8OUVYWmrz24yqdomhZHpSyLYmqiQwEJ'
    });
    
    this.db = this.app.database();
    logger.info('CloudBase SDK initialized successfully');
  }

  /**
   * 查询用户信息
   */
  async getUserByOpenid(openid) {
    try {
      logger.info('Querying user from cloud database:', { 
        openid: openid?.substring(0, 10) + '...' 
      });

      const result = await this.db.collection('users').where({
        _openid: openid
      }).get();

      if (result.data && result.data.length > 0) {
        const user = result.data[0];
        logger.info('User found in cloud database:', {
          openid: openid?.substring(0, 10) + '...',
          reward: user.reward || 0
        });

        return {
          success: true,
          data: {
            _id: user._id,
            openid: user._openid,
            nickName: user.nickName || 'User',
            reward: user.reward || 0,
            createTime: user.createTime
          }
        };
      } else {
        logger.warn('User not found in cloud database:', { 
          openid: openid?.substring(0, 10) + '...' 
        });
        return {
          success: false,
          message: 'User not found'
        };
      }
    } catch (error) {
      logger.error('Failed to query user from cloud database:', error);
      throw error;
    }
  }

  /**
   * 更新用户积分
   */
  async updateUserPoints(openid, points) {
    try {
      logger.info('Updating user points in cloud database:', {
        openid: openid?.substring(0, 10) + '...',
        points: points
      });

      // 先查询用户当前积分
      const userResult = await this.getUserByOpenid(openid);
      if (!userResult.success) {
        throw new Error('User not found');
      }

      const currentPoints = userResult.data.reward || 0;
      const newPoints = currentPoints + points;

      logger.info('Points calculation details:', {
        openid: openid?.substring(0, 10) + '...',
        currentPoints,
        pointsToAdd: points,
        calculatedNewPoints: newPoints
      });

      // 使用官方SDK更新用户积分
      const updateResult = await this.db.collection('users').where({
        _openid: openid
      }).update({
        reward: newPoints,
        lastRechargeTime: new Date()
      });

      logger.info('User points update result:', {
        openid: openid?.substring(0, 10) + '...',
        updated: updateResult.updated || 0,
        requestId: updateResult.requestId
      });

      if (updateResult.updated > 0) {
        logger.info('User points updated successfully in cloud database:', {
          openid: openid?.substring(0, 10) + '...',
          oldPoints: currentPoints,
          addedPoints: points,
          newPoints: newPoints,
          recordsUpdated: updateResult.updated
        });

        return {
          success: true,
          message: `Successfully updated ${points} points for user`,
          data: {
            oldPoints: currentPoints,
            addedPoints: points,
            newPoints: newPoints
          }
        };
      } else {
        throw new Error(`No records updated: ${JSON.stringify(updateResult)}`);
      }
    } catch (error) {
      logger.error('Failed to update user points in cloud database:', error);
      throw error;
    }
  }

  /**
   * 创建积分日志
   */
  async createPointLog(logData) {
    try {
      logger.info('Creating point log in cloud database:', {
        orderNo: logData.orderNo,
        points: logData.points
      });

      const pointLog = {
        order_no: logData.orderNo,
        openid: logData.openid,
        type: 'recharge',
        amount: logData.points,
        description: `充值获得${logData.points}积分`,
        transaction_id: logData.transactionId || '',
        create_time: new Date(),
        status: logData.status || 'processing'
      };

      // 使用官方SDK创建积分日志
      const result = await this.db.collection('point_logs').add(pointLog);

      if (result.id) {
        logger.info('Point log created successfully in cloud database:', {
          id: result.id,
          requestId: result.requestId
        });
        return {
          success: true,
          data: {
            _id: result.id
          }
        };
      } else {
        throw new Error(`Create point log failed: ${JSON.stringify(result)}`);
      }
    } catch (error) {
      logger.error('Failed to create point log in cloud database:', error);
      throw error;
    }
  }

  /**
   * 检查订单是否已存在
   */
  async checkOrderExists(orderNo) {
    try {
      logger.info('Checking order in cloud database:', { orderNo });
      
      const result = await this.db.collection('point_logs').where({
        order_no: orderNo
      }).get();

      const exists = result.data && result.data.length > 0;
      logger.info('Order check result from cloud database:', { 
        orderNo, 
        exists, 
        foundRecords: result.data ? result.data.length : 0
      });
      
      return exists;
    } catch (error) {
      logger.error('Failed to check order existence:', error);
      return false;
    }
  }

  /**
   * 更新积分日志状态
   */
  async updatePointLogStatus(orderNo, status, transactionId) {
    try {
      const result = await this.db.collection('point_logs').where({
        order_no: orderNo
      }).update({
        status: status,
        transaction_id: transactionId,
        update_time: new Date()
      });
      
      if (result.updated > 0) {
        logger.info('Point log status updated successfully:', {
          orderNo,
          status,
          updated: result.updated
        });
        return { success: true };
      } else {
        throw new Error(`No records updated: ${JSON.stringify(result)}`);
      }
    } catch (error) {
      logger.error('Failed to update point log status:', error);
      throw error;
    }
  }
}

module.exports = new CloudDatabaseService();
