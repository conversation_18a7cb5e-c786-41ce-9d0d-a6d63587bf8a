// 引入腾讯地图工具类
const mapUtils = require('../../utils/mapUtils.js');
// 引入图片处理工具
const imageUtils = require('../supply/publish/imageUtils');
// 引入表单处理工具
const formUtils = require('./formUtils.js');
// 引入搜索工具
const searchUtils = require('../../utils/searchUtils');
// 引入统计工具
const statsUtils = require('../../utils/statsUtils');
// 引入内容安全检测工具
const contentSecurityUtils = require('../../utils/contentSecurityUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      title: '',
      content: '',
      price: '',
      localPrice: '', // 添加地价字段
      price_unit: '棵',
      category: '常见',
      contactName: '',
      contactPhone: '',
      location: '',
      // 作物参数
      cropVariety: '',
      growthStage: '中等',
      quantity: '',
      unit: '株',
      // 新增栽培状态参数
      plant_method: '',
      // 苗木规格参数
      height: '',         // 高度
      trunkDiameter: '',  // 米径
      branchPoint: '',    // 分枝点
      crownWidth: '',     // 冠幅
      groundDiameter: '', // 地径
      chestDiameter: '',  // 胸径
      cupSize: '',        // 杯口
      mainVineLength: '', // 主蔓长度
      branchCount: '',    // 分支数
      plantAge: '',       // 苗龄
      plantDensity: '',   // 株/m²
      clumpCount: '',     // 丛生
      clumpDiameter: ''   // 杆径
    },
    // 分类选项 - 融合乔木和灌木为常见
    categoryOptions: ['常见', '藤本类', '草皮类', '花草', '种子'],
    // 产品质量选项
    growthStageOptions: ['精品', '中等', '一般'],
    // 单位选项
    unitOptions: ['株', '盆', '斤', '公斤', '箱', '件', '包'],
    // 新增价格单位选项
    priceUnitOptions: ['株', '公斤', '棵', '斤', '平方米', '厘米', '袋', '捆','杯'],
    // 继续发布弹窗控制
    showContinueModal: false,
    // 新增栽培状态选项
    plantMethodOptions: ['地栽苗', '小杯苗', '大杯苗', '袋装苗'],
    // 图片列表
    imageList: [],
    // 是否正在提交
    submitting: false,
    // 表单验证错误
    errors: {},
    // 植物名推荐相关
    plantSuggestions: [],
    showSuggestions: false,
    searchTimer: null,
    
    // 地区选择器相关
    areaVisible: false,
    areaValue: [],
    areaText: '',
    provinces: [],
    cities: [],
    
    // 规格显示控制
    specVisibility: {
      height: true,         // 高度默认显示
      crownWidth: true,     // 冠幅默认显示
      trunkDiameter: true,  // 米径默认显示
      groundDiameter: true, // 地径默认显示
      branchPoint: true,    // 分枝点默认显示
      chestDiameter: true,  // 胸径默认显示
      cupSize: true,        // 杯口默认显示
      mainVineLength: true, // 主蔓长度默认显示
      branchCount: true,    // 分支数默认显示
      plantAge: true,       // 苗龄默认显示
      plantDensity: false,  // 株/m²默认不显示
      clumpCount: true,     // 丛生默认显示
      clumpDiameter: true,  // 杆径默认显示
      seedWeight: false     // 种子重量默认不显示
    },
    // 规格必填项
    requiredSpecs: {
      height: true,         // 高度默认必填
      crownWidth: false,    // 冠幅默认必填
      trunkDiameter: true, 
      branchPoint: false,
      groundDiameter: false,
      chestDiameter: false,
      cupSize: false,
      mainVineLength: false,
      branchCount: false,
      plantAge: false,
      plantDensity: false,  // 株/m²默认不必填
      clumpCount: false,    // 丛生默认不必填
      clumpDiameter: false  // 杆径默认不必填
    },
    // 页面是否已初始化
    isInitialized: false,
    // 输入框是否已经聚焦
    titleInputFocused: false,
    // 是否应该聚焦到标题输入框
    shouldFocusTitleInput: false,
    // 定位是否失败
    locationFailed: false,
    // 是否正在刷新位置
    refreshing: false
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查用户是否已登录
    if (!this.checkLogin()) {
      return;
    }

    // 初始化页面
    this.initPage();
    // 初始化地区数据
    this.initAreaData();

    // 处理继续发布的参数
    if (options.phone) {
      this.setData({
        'formData.contactPhone': options.phone
      });
    }

    if (options.location && options.areaText && options.areaValue) {
      try {
        const areaValue = JSON.parse(options.areaValue);
        this.setData({
          'formData.location': decodeURIComponent(options.location),
          areaText: decodeURIComponent(options.areaText),
          areaValue: areaValue
        });
      } catch (e) {
        console.error('解析地区参数失败:', e);
        // 设置默认地区值
        this.setDefaultArea();
      }
    } else {
      // 设置默认地区值
      this.setDefaultArea();
    }

    // 设置默认分类对应的规格显示
    this.updateSpecsByCategory(this.data.formData.category);

    // 标记页面已初始化
    this.setData({
      isInitialized: true
    });
  },

  /**
   * 处理返回上一页
   */
  handleBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 检查用户是否已登录
   */
  checkLogin: function() {
    // 不再检查登录状态，直接返回 true
    return true;
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 获取App实例
    const app = getApp();
    
    // 获取用户信息
    let userInfo = app.globalData.userInfo;
    const userId = app.globalData.userId;
    
    // 如果全局状态没有，则从本地存储获取
    if (!userInfo) {
      userInfo = wx.getStorageSync('userInfo');
    }
    
    // 如果有用户信息，自动填充联系人
    if (userInfo) {
      // 如果用户信息中已有电话号码，直接使用
      if (userInfo.phoneNumber) {
        this.setData({
          'formData.contactPhone': userInfo.phoneNumber
        });
      }
    }
    
    // 获取位置信息
    this.getLocationInfo();
  },

  /**
   * 跳转到个人信息页面并触发编辑窗口
   */
  redirectToUserProfile() {
    wx.showModal({
      title: '完善个人信息',
      content: '请先完善个人信息（特别是电话号码）后再发布供应信息',
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          // 设置全局标记，让用户页面知道需要弹出编辑窗口
          const app = getApp();
          app.globalData.shouldShowUserEdit = true;
          
          wx.switchTab({
            url: '/pages/user/user'
          });
        }
      }
    });
  },

  /**
   * 获取位置信息
   */
  getLocationInfo() {
    mapUtils.getCurrentLocationAndReverse({
      success: (result) => {
        if (result.status === 0) {
          const addressComponent = result.result.address_component;
          const province = addressComponent.province;
          const city = addressComponent.city;
          const district = addressComponent.district;
          
          // 查找省份对应的code
          const provinces = this.data.provinces || [];
          let provinceCode = '';
          let cityCode = '';
          
          for (let i = 0; i < provinces.length; i++) {
            if (provinces[i].label.includes(province)) {
              provinceCode = provinces[i].value;
              break;
            }
          }
          
          // 如果找到省份代码，获取对应的城市列表
          if (provinceCode) {
            const cities = this.getCities(provinceCode, { cities: this.getCityData() });
            
            // 查找城市对应的code
            for (let i = 0; i < cities.length; i++) {
              if (cities[i].label.includes(city)) {
                cityCode = cities[i].value;
                break;
              }
            }
            
            // 更新地区选择器的值和显示文本
            if (provinceCode && cityCode) {
              const fullLocation = province + city + (district ? district : '');
              const displayText = province + ' ' + city + (district ? ' ' + district : '');
              
              this.setData({
                areaValue: [provinceCode, cityCode],
                areaText: displayText,
                'formData.location': fullLocation,
                cities: cities,
                'errors.location': '', // 清除地区相关的错误提示
                locationFailed: false // 定位成功，隐藏失败提示
              });
            }
          }
        } else {
          // 定位失败，显示失败提示和刷新按钮
          this.setData({
            locationFailed: true
          });
        }
      },
      fail: (error) => {
        // 定位失败，显示失败提示和刷新按钮（不显示toast，避免干扰用户）
        this.setData({
          locationFailed: true
        });
      }
    });
  },

  /**
   * 刷新位置信息
   */
  onRefreshLocation() {
    // 设置刷新状态，开始旋转动画
    this.setData({
      refreshing: true
    });

    // 显示加载提示
    wx.showLoading({
      title: '正在获取位置...',
      mask: true
    });

    // 重新获取位置信息
    mapUtils.getCurrentLocationAndReverse({
      success: (result) => {
        wx.hideLoading();
        if (result.status === 0) {
          const addressComponent = result.result.address_component;
          const province = addressComponent.province;
          const city = addressComponent.city;
          const district = addressComponent.district;

          // 查找省份对应的code
          const provinces = this.data.provinces || [];
          let provinceCode = '';
          let cityCode = '';

          for (let i = 0; i < provinces.length; i++) {
            if (provinces[i].label.includes(province)) {
              provinceCode = provinces[i].value;
              break;
            }
          }

          // 如果找到省份代码，获取对应的城市列表
          if (provinceCode) {
            const cities = this.getCities(provinceCode, { cities: this.getCityData() });

            // 查找城市对应的code
            for (let i = 0; i < cities.length; i++) {
              if (cities[i].label.includes(city)) {
                cityCode = cities[i].value;
                break;
              }
            }

            // 更新地区选择器的值和显示文本
            if (provinceCode && cityCode) {
              const fullLocation = province + city + (district ? district : '');
              const displayText = province + ' ' + city + (district ? ' ' + district : '');

              this.setData({
                areaValue: [provinceCode, cityCode],
                areaText: displayText,
                'formData.location': fullLocation,
                cities: cities,
                'errors.location': '', // 清除地区相关的错误提示
                locationFailed: false, // 定位成功，隐藏失败提示
                refreshing: false // 停止旋转动画
              });

              wx.showToast({
                title: '位置获取成功',
                icon: 'success',
                duration: 2000
              });
            }
          }
        } else {
          // 定位仍然失败，停止旋转动画
          this.setData({
            refreshing: false
          });
          wx.showToast({
            title: '定位失败，请检查定位权限',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        // 停止旋转动画
        this.setData({
          refreshing: false
        });

        // 根据错误类型显示不同的提示
        let errorMsg = '定位失败，请重试';
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          errorMsg = '请在设置中开启定位权限';
        } else if (error.errMsg && error.errMsg.includes('fail')) {
          errorMsg = '定位服务不可用，请检查GPS设置';
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 不再检查登录状态
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 不再清除数据，保留页面状态
    // 仅在必要时清除数据（如退出页面时）
  },
  
  /**
   * 清除页面数据
   */
  clearPageData: function() {
    // 保存当前的联系电话
    const currentPhone = this.data.formData.contactPhone;
    
    this.setData({
      // 重置表单数据
      formData: {
        title: '',
        content: '',
        price: '',
        price_unit: '棵',
        category: '乔木',
        contactName: '',
        contactPhone: currentPhone, // 保留联系电话
        location: '',
        cropVariety: '',
        growthStage: '精品',
        quantity: '',
        unit: '株',
        plant_method: '',
        height: '',
        trunkDiameter: '',
        branchPoint: '',
        crownWidth: '',
        groundDiameter: '',
        chestDiameter: '',
        cupSize: '',
        mainVineLength: '',
        branchCount: '',
        plantAge: '',
        plantDensity: ''
      },
      // 清除图片列表
      imageList: [],
      // 重置错误状态
      errors: {},
      // 清除植物名推荐
      plantSuggestions: [],
      showSuggestions: false,
      // 重置地区选择器
      areaText: '',
      areaValue: [],
      // 重置初始化标记
      isInitialized: false
    });
    
    // 如果有搜索定时器，清除它
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer);
      this.data.searchTimer = null;
    }
    
   
  },

  /**
   * 返回上一页 - 通过导航栏的返回按钮自动处理
   */
  /* onBackTap() {
    wx.navigateBack();
  }, */

  /**
   * 返回首页
   */
  onBackHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 处理输入框变化
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const priceType = e.currentTarget.dataset.priceType; // 获取价格类型
    
    // 如果是标题字段，使用全局搜索工具处理
    if (field === 'title') {
      searchUtils.handleInputChange(this, e);
    } else {
      // 不再处理上车价和地价互斥逻辑，直接更新对应字段
      this.setData({
        [`formData.${field}`]: value,
        [`errors.${field}`]: '' // 清除对应字段的错误
      });
    }
    
    // 对特定字段进行实时验证
    this.validateField(field, value);
    
    // 检查必填项是否已填写，如果已填写则清除红色边框
    if (field === 'height' || field === 'crownWidth' || field === 'trunkDiameter' || 
        field === 'branchPoint' || field === 'groundDiameter' || field === 'chestDiameter' || 
        field === 'cupSize' || field === 'mainVineLength' || field === 'branchCount' || 
        field === 'plantAge' || field === 'plantDensity' || field === 'quantity' ||
        field === 'clumpCount' || field === 'clumpDiameter') {
      
      // 如果值不为空，则清除对应字段的错误提示
      if (value && value.trim() !== '') {
        this.setData({
          [`errors.${field}`]: ''
        });
      }
    }
  },

  /**
   * 验证单个字段
   */
  validateField(field, value) {
    let error = '';
    const requiredSpecs = this.data.requiredSpecs;
    
    switch (field) {
      case 'title':
        if (!value.trim()) {
          error = '请输入标题';
        }
        break;
        
      case 'price':
        if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的价格';
        }
        break;
        
      case 'height':
        if (requiredSpecs.height && !value) {
          error = '请输入高度';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的高度';
        }
        break;
        
      case 'crownWidth':
        if (requiredSpecs.crownWidth && !value) {
          error = '请输入冠幅';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的冠幅';
        }
        break;
        
      case 'trunkDiameter':
        if (requiredSpecs.trunkDiameter && !value) {
          error = '请输入米径';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的米径';
        }
        break;
        
      case 'branchPoint':
        if (requiredSpecs.branchPoint && !value) {
          error = '请输入分枝点';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的分枝点';
        }
        break;
        
      case 'mainVineLength':
        if (requiredSpecs.mainVineLength && !value) {
          error = '请输入主蔓长度';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的主蔓长度';
        }
        break;
        
      case 'branchCount':
        if (requiredSpecs.branchCount && !value) {
          error = '请输入分支数';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的分支数';
        }
        break;
        
      case 'groundDiameter':
        if (requiredSpecs.groundDiameter && !value) {
          error = '请输入地径';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的地径';
        }
        break;
        
      case 'chestDiameter':
        if (requiredSpecs.chestDiameter && !value) {
          error = '请输入胸径';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的胸径';
        }
        break;
        
      case 'cupSize':
        if (requiredSpecs.cupSize && !value) {
          error = '请输入杯口';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的杯口';
        }
        break;
        
      case 'plantAge':
        if (requiredSpecs.plantAge && !value) {
          error = '请输入苗龄';
        }
        break;
        
      case 'quantity':
        if (!value) {
          error = '请输入供应数量';
        }
        break;
        
      case 'content':
        // 移除内容必填验证
        break;
        
     
        
      case 'contactPhone':
        if (value && !/^1\d{10}$/.test(value)) {
          error = '请输入正确的11位手机号码';
        }
        break;
        
      case 'plantDensity':
        if (requiredSpecs.plantDensity && !value) {
          error = '请输入密度(株/m²)';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的密度';
        }
        break;
        
      case 'clumpCount':
        if (requiredSpecs.clumpCount && !value) {
          error = '请输入丛生数量';
        } else if (value && (isNaN(parseInt(value)) || parseInt(value) <= 0)) {
          error = '请输入有效的丛生数量';
        }
        break;
        
      case 'clumpDiameter':
        if (requiredSpecs.clumpDiameter && !value) {
          error = '请输入杆径';
        } else if (value && (isNaN(parseFloat(value)) || parseFloat(value) <= 0)) {
          error = '请输入有效的杆径';
        }
        break;
    }
    
    if (error) {
      this.setData({
        [`errors.${field}`]: error
      });
    }
  },

  // 使用全局搜索工具，已移除searchPlants函数

  /**
   * 处理植物名输入框点击事件
   */
  onTitleClick() {
    // 如果输入框已经获得焦点，则不做任何操作
    if (this.data.titleInputFocused) {
      return;
    }

    // 获取当前窗口信息
    const windowInfo = wx.getWindowInfo();
    const windowHeight = windowInfo.windowHeight;
    
    // 滚动屏幕高度的一半，为搜索提示留出空间
    const scrollToPosition = windowHeight / 3;
    
    // 滚动到指定位置
    wx.pageScrollTo({
      scrollTop: scrollToPosition,
      duration: 300
    });
  },

  /**
   * 处理输入框获得焦点
   */
  onTitleFocus() {
    // 标记输入框已获得焦点
    this.setData({
      titleInputFocused: true
    });
    
    // 调用搜索工具处理焦点事件
    searchUtils.handleTitleFocus(this);
    
    // 获取当前窗口信息
    const windowInfo = wx.getWindowInfo();
    const windowHeight = windowInfo.windowHeight;
    
    // 滚动屏幕高度的一半，为搜索提示留出空间
    const scrollToPosition = windowHeight / 3;
    
    // 滚动到指定位置
    wx.pageScrollTo({
      scrollTop: scrollToPosition,
      duration: 300
    });
  },

  /**
   * 处理输入框失去焦点
   */
  onTitleBlur() {
    // 标记输入框已失去焦点
    this.setData({
      titleInputFocused: false
    });
    
    // 调用搜索工具处理失焦事件
    searchUtils.handleTitleBlur(this);
  },

  /**
   * 选择推荐的植物名
   */
  onSelectSuggestion(e) {
    searchUtils.handleSelectSuggestion(this, e, true); // 自动填充品种
  },

  /**
   * 关闭植物名称推荐列表
   */
  closeSuggestions() {
    searchUtils.closeSuggestions(this);
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    searchUtils.stopPropagation(e);
  },

  /**
   * 处理分类选择
   */
  onCategoryChange(e) {
    const category = this.data.categoryOptions[e.detail.value];
    const oldCategory = this.data.formData.category;
    
    // 如果分类发生变化，清空所有规格参数
    if (oldCategory && oldCategory !== category) {
      // 创建一个新的formData对象，清空所有规格参数
      const updatedFormData = {
        ...this.data.formData,
        category: category,
        // 清空所有规格参数
        height: '',
        trunkDiameter: '',
        branchPoint: '',
        crownWidth: '',
        groundDiameter: '',
        chestDiameter: '',
        cupSize: '',
        mainVineLength: '',
        branchCount: '',
        plantAge: '',
        plantDensity: '',
        clumpCount: '',
        clumpDiameter: ''
      };
      
      this.setData({
        formData: updatedFormData,
        'errors.category': ''
      });
    } else {
      // 如果是首次选择分类，不需要清空参数
      this.setData({
        'formData.category': category,
        'errors.category': ''
      });
    }
    
    // 根据选择的分类更新必填规格
    this.updateSpecsByCategory(category);
  },

  /**
   * 根据分类更新规格显示和必填项
   */
  updateSpecsByCategory(category) {
    // 使用formUtils模块更新规格显示和必填项
    const { specVisibility, requiredSpecs } = formUtils.updateSpecsByCategory(category);
    
    this.setData({
      specVisibility,
      requiredSpecs
    });
  },

  /**
   * 处理生长阶段radio选择
   */
  onGrowthStageRadioChange(e) {
    this.setData({
      'formData.growthStage': e.detail.value,
      'errors.growthStage': ''
    });
  },

  /**
   * 处理单位选择
   */
  onUnitChange(e) {
    this.setData({
      'formData.unit': this.data.unitOptions[e.detail.value],
      'errors.unit': ''
    });
  },

  /**
   * 拍照上传
   */
  takePhoto() {
    // 调用相机API
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: async (res) => {
        // 将拍摄的照片添加到图片列表
        const tempFiles = res.tempFiles;
        if (tempFiles.length > 0) {
          const { imageList } = this.data;
          // 检查是否超过6张图片限制
          if (imageList.length + tempFiles.length > 6) {
            wx.showToast({
              title: '最多上传6张图片',
              icon: 'none'
            });
            return;
          }
          
          wx.showLoading({
            title: '添加水印中...',
            mask: true
          });
          
          // 设置超时处理
          const timeoutPromise = new Promise(resolve => {
            setTimeout(() => {
              resolve('timeout');
            }, 5000); // 5秒超时
          });
          
          try {
            // 为图片添加时间水印，带超时处理
            const imagePaths = tempFiles.map(file => file.tempFilePath);
            
            // 使用Promise.race实现超时处理
            const result = await Promise.race([
              imageUtils.addWatermarkToImages(imagePaths),
              timeoutPromise
            ]);
            
            // 如果是超时结果，使用原图
            if (result === 'timeout') {
              console.log('添加水印超时，使用原图');
              this.setData({
                imageList: [...imageList, ...imagePaths],
                'errors.imageList': ''
              });
              
              wx.showToast({
                title: '水印处理超时',
                icon: 'none',
                duration: 2000
              });
            } else {
              // 添加新拍摄的照片（带水印）
              this.setData({
                imageList: [...imageList, ...result],
                'errors.imageList': ''
              });
            }
          } catch (error) {
            console.error('添加水印失败:', error);
            // 如果添加水印失败，使用原图
            this.setData({
              imageList: [...imageList, ...tempFiles.map(file => file.tempFilePath)],
              'errors.imageList': ''
            });
            
            wx.showToast({
              title: '水印添加失败',
              icon: 'none',
              duration: 2000
            });
          } finally {
            wx.hideLoading();
          }
        }
      },
      fail: (err) => {
        console.error('拍照失败:', err);
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      },
      complete: () => {
        // 不执行任何清空操作，保留当前页面状态
      }
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const { imageList } = this.data;
    const remainCount = 6 - imageList.length;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传6张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album'],
      sizeType: ['compressed'],
      success: async (res) => {
        wx.showLoading({
          title: '添加水印中...',
          mask: true
        });
        
        // 设置超时处理
        const timeoutPromise = new Promise(resolve => {
          setTimeout(() => {
            resolve('timeout');
          }, 5000); // 5秒超时
        });
        
        try {
          // 为图片添加时间水印
          const imagePaths = res.tempFiles.map(file => file.tempFilePath);
          
          // 使用Promise.race实现超时处理
          const result = await Promise.race([
            imageUtils.addWatermarkToImages(imagePaths),
            timeoutPromise
          ]);
          
          // 如果是超时结果，使用原图
          if (result === 'timeout') {
            console.log('添加水印超时，使用原图');
            this.setData({
              imageList: [...imageList, ...imagePaths],
              'errors.imageList': ''
            });
            
            wx.showToast({
              title: '水印处理超时',
              icon: 'none',
              duration: 2000
            });
          } else {
            // 将选择的图片添加到图片列表（带水印）
            this.setData({
              imageList: [...imageList, ...result],
              'errors.imageList': ''
            });
          }
        } catch (error) {
          console.error('添加水印失败:', error);
          // 如果添加水印失败，使用原图
          this.setData({
            imageList: [...imageList, ...res.tempFiles.map(file => file.tempFilePath)],
            'errors.imageList': ''
          });
          
          wx.showToast({
            title: '水印添加失败',
            icon: 'none',
            duration: 2000
          });
        } finally {
          wx.hideLoading();
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      },
      complete: () => {
        // 不执行任何清空操作，保留当前页面状态
      }
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { imageList } = this.data;
    
    wx.previewImage({
      current: imageList[index],
      urls: imageList
    });
  },

  /**
   * 删除图片
   */
  deleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const { imageList } = this.data;
    
    imageList.splice(index, 1);
    
    this.setData({
      imageList
    });
  },

  /**
   * 验证表单
   */
  validateForm() {
    const { formData, imageList, requiredSpecs } = this.data;
    
    // 使用formUtils模块验证表单
    const errors = formUtils.validateForm(formData, imageList, requiredSpecs);
    
 
    // 验证联系电话
    if (!formData.contactPhone) {
      errors.contactPhone = '请输入联系电话';
    } else if (!/^1\d{10}$/.test(formData.contactPhone)) {
      errors.contactPhone = '请输入正确的11位手机号码';
    }
    
    // 地区验证已移除 - 地区输入现在是可选的
    
    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 滚动到第一个错误的位置并显示提示
   */
  scrollToError() {
    const errors = this.data.errors;
    const firstErrorField = Object.keys(errors)[0];
    
    if (!firstErrorField) return;
    
    // 错误字段到表单字段的映射
    const fieldMapping = {
      title: '植物名',
      price: '上车价',
      localPrice: '地价',
      height: '高度',
      crownWidth: '冠幅',
      growthStage: '产品质量',
      quantity: '供应数量',
      content: '详细描述',
      imageList: '图片',
      contactName: '联系人',
      contactPhone: '联系电话',
      location: '所在地区'
    };
    
    // 获取错误字段对应的表单字段名称
    const fieldName = fieldMapping[firstErrorField] || firstErrorField;
    const errorMessage = errors[firstErrorField];
    
    // 构建完整错误信息
    const fullErrorMessage = `${fieldName}：${errorMessage}`;
    
    // 显示错误提示对话框
    wx.showModal({
      title: '请完善表单信息',
      content: fullErrorMessage,
      showCancel: false,
      success: () => {
        // 对话框关闭后，滚动到错误字段位置
        wx.pageScrollTo({
          selector: `.${firstErrorField}`,
          duration: 300
        });
      }
    });
  },

  /**
   * 提交表单
   */
  submitForm() {
    // 检查是否还在提交中
    if (this.data.submitting) {
      return;
    }

    // ✅ 立即设置提交状态，防止重复点击
    this.setData({ submitting: true });

    // 首先检查用户是否已登录
    const app = getApp();
    const isLogined = app.globalData.isLogined || wx.getStorageSync('isLogined');
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    const userId = app.globalData.userId || wx.getStorageSync('userId');
    
    if (!isLogined || !userInfo || !userId) {
      // 用户未登录，重置状态并显示提示
      this.setData({ submitting: false });
      wx.showModal({
        title: '提示',
        content: '请先登录后再发布供应信息',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            // 跳转到用户中心页面
            wx.switchTab({
              url: '/pages/user/user'
            });
          }
        }
      });
      return;
    }
    
    // 验证上车价和地价至少有一个填写
    if (!this.data.formData.price && !this.data.formData.localPrice) {
      this.setData({ submitting: false });
      wx.showToast({
        title: '请至少填写上车价或地价',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 检查是否至少填写了一个规格参数
    const { formData } = this.data;
    const specFields = [
      'height', 'trunkDiameter', 'crownWidth', 'branchPoint', 'groundDiameter',
      'chestDiameter', 'cupSize', 'mainVineLength', 'branchCount', 'plantAge',
      'plantDensity', 'clumpCount', 'clumpDiameter'
    ];
    
    // 种子分类不需要填写规格参数
    if (formData.category === '种子') {
      // 直接跳过规格参数验证
    } else {
      // 其他分类需要至少填写一个规格参数
      let hasAnySpec = false;
      for (const field of specFields) {
        if (formData[field] && formData[field].trim() !== '') {
          hasAnySpec = true;
          break;
        }
      }
      
      if (!hasAnySpec) {
        this.setData({ submitting: false });
        wx.showToast({
          title: '请至少填写一个苗木规格参数',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }
    
    // 然后进行表单验证
    if (!this.validateForm()) {
      this.setData({ submitting: false });
      // 滚动到第一个错误的位置并显示提示
      this.scrollToError();
      return;
    }

    // 进行内容安全检测
    this.checkContentSecurity();
  },

  /**
   * 检测内容安全
   */
  async checkContentSecurity() {
    // 显示检测提示
    wx.showLoading({
      title: '正在检测内容...',
      mask: true
    });

    try {
      // 获取用户openid
      const app = getApp();
      const openid = app.globalData.openid || wx.getStorageSync('openid');

      if (!openid) {
        wx.hideLoading();
        this.setData({ submitting: false });
        wx.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 检测退林还耕发布内容
      const checkResult = await contentSecurityUtils.checkSupplyPublishContent(
        this.data.formData,
        openid
      );

      wx.hideLoading();

      if (!checkResult.success) {
        // 检测服务异常
        wx.showModal({
          title: '提示',
          content: '内容检测服务异常，是否继续发布？',
          success: (res) => {
            if (res.confirm) {
              this.showSubmitConfirm();
            }
          }
        });
        return;
      }

      if (checkResult.hasRiskyContent) {
        // 发现敏感内容，阻止发布
        this.setData({ submitting: false });
        const errorMessage = this.generateSimpleErrorMessage(checkResult.riskyFields);
        wx.showModal({
          title: '内容审核未通过',
          content: errorMessage,
          showCancel: false,
          confirmText: '我知道了'
        });
        return;
      }

      // 内容安全，继续发布流程
      this.showSubmitConfirm();

    } catch (error) {
      wx.hideLoading();
      this.setData({ submitting: false });
      console.error('内容安全检测失败:', error);

      wx.showToast({
        title: '内容检测失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 显示提交确认框
   */
  showSubmitConfirm() {
    wx.showModal({
      title: '确认提交',
      content: '确定要发布这条退林还耕信息吗？',
      success: (res) => {
        if (res.confirm) {
          // 设置提交状态
          this.setData({ submitting: true });

          // 显示加载提示
          wx.showLoading({
            title: '正在提交...',
            mask: true
          });

          this.doSubmit();
        }
      }
    });
  },

  /**
   * 生成简化的错误提示消息
   */
  generateSimpleErrorMessage(riskyFields) {
    if (!riskyFields || riskyFields.length === 0) {
      return '输入内容包含不当信息，请检查并重新输入';
    }

    const fieldNames = [];
    riskyFields.forEach(item => {
      const fieldName = contentSecurityUtils.getFieldDescription(item.field);
      if (!fieldNames.includes(fieldName)) {
        fieldNames.push(fieldName);
      }
    });

    if (fieldNames.length === 1) {
      return `您输入的"${fieldNames[0]}"存在不当内容，请检查并重新输入`;
    } else if (fieldNames.length === 2) {
      return `您输入的"${fieldNames[0]}"或"${fieldNames[1]}"存在不当内容，请检查并重新输入`;
    } else {
      return `您输入的"${fieldNames.join('"、"')}"存在不当内容，请检查并重新输入`;
    }
  },

  /**
   * 提交表单数据
   */
  doSubmit() {
    const { formData } = this.data;
    
    // 使用mapUtils获取位置和省份信息
    mapUtils.getCurrentLocationAndReverse({
      success: (result) => {
        if (result.status === 0) {
          const addressComponent = result.result.address_component;
          const province = addressComponent.province;
          const latitude = result.result.location.lat;
          const longitude = result.result.location.lng;
          
          // 将省份信息添加到formData
          formData.province = province;
          
          // 获取到位置后，继续上传图片和保存数据
          this.uploadImagesAndSaveData(formData, longitude, latitude);
        } else {
          // 如果逆地址解析失败，尝试使用普通位置
          wx.getLocation({
            type: 'wgs84',
            isHighAccuracy: true,
            highAccuracyExpireTime: 3000,
            success: (res) => {
              const latitude = res.latitude;
              const longitude = res.longitude;
              
              // 获取到位置后，继续上传图片和保存数据（但没有省份信息）
              this.uploadImagesAndSaveData(formData, longitude, latitude);
            },
            fail: (err) => {
              console.error('获取位置失败:', err);
              wx.showModal({
                title: '提示',
                content: '获取位置信息失败，是否继续发布？',
                success: (res) => {
                  if (res.confirm) {
                    // 用户选择继续，则不带位置信息发布
                    this.uploadImagesAndSaveData(formData);
                  } else {
                    // 用户选择取消，则停止发布
                    wx.hideLoading();
                    this.setData({ submitting: false });
                  }
                }
              });
            }
          });
        }
      },
      fail: (err) => {
        console.error('逆地址解析失败:', err);
        // 如果逆地址解析失败，尝试使用普通位置
        wx.getLocation({
          type: 'wgs84',
          isHighAccuracy: true,
          highAccuracyExpireTime: 3000,
          success: (res) => {
            const latitude = res.latitude;
            const longitude = res.longitude;
            
            // 获取到位置后，继续上传图片和保存数据（但没有省份信息）
            this.uploadImagesAndSaveData(formData, longitude, latitude);
          },
          fail: (err) => {
            console.error('获取位置失败:', err);
            wx.showModal({
              title: '提示',
              content: '获取位置信息失败，是否继续发布？',
              success: (res) => {
                if (res.confirm) {
                  // 用户选择继续，则不带位置信息发布
                  this.uploadImagesAndSaveData(formData);
                } else {
                  // 用户选择取消，则停止发布
                  wx.hideLoading();
                  this.setData({ submitting: false });
                }
              }
            });
          }
        });
      }
    });
  },

  /**
   * 上传图片并保存数据到数据库
   */
  uploadImagesAndSaveData(formData, longitude, latitude) {
    // 获取当前用户信息
    const app = getApp();
    const userInfo = app.globalData.userInfo || wx.getStorageSync('userInfo');
    const userId = app.globalData.userId || wx.getStorageSync('userId');
    
    // 首先创建帖子记录获取帖子ID
    const db = wx.cloud.database();
    
    // 构建供应数据（不包含图片列表）
    const supplyData = {
      title: formData.title.trim(),
      price: formData.price ? parseFloat(formData.price) : 0, // 上车价
      land_price: formData.localPrice ? parseFloat(formData.localPrice) : 0, // 添加地价字段
      price_unit: formData.price_unit, // 添加价格单位
      category: formData.category,
      height: formData.height ? parseFloat(formData.height) : null,
      canopy: formData.crownWidth ? parseFloat(formData.crownWidth) : null, // 冠幅
      meter_diameter: formData.trunkDiameter ? parseFloat(formData.trunkDiameter) : null, // 米径
      branchPos: formData.branchPoint ? parseFloat(formData.branchPoint) : null, // 分支点
      ground_diameter: formData.groundDiameter ? parseFloat(formData.groundDiameter) : null, // 地径
      thorax_diameter: formData.chestDiameter ? parseFloat(formData.chestDiameter) : null, // 胸径
      cup: formData.cupSize ? parseFloat(formData.cupSize) : null, // 杯口
      main_vine_length: formData.mainVineLength ? parseFloat(formData.mainVineLength) : null, // 主蔓长度
      branch_count: formData.branchCount ? parseInt(formData.branchCount) : null, // 分支数
      plant_age: formData.plantAge ? formData.plantAge : null, // 苗龄
      plant_density: formData.plantDensity ? parseFloat(formData.plantDensity) : null, // 株/m²
      plant_method: formData.plant_method || null, // 添加栽培状态
      quality: formData.growthStage, // 产品质量
      clumpCount: formData.clumpCount ? parseInt(formData.clumpCount) : null, // 丛生(杆)
      clumpDiameter: formData.clumpDiameter ? parseFloat(formData.clumpDiameter) : null, // 杆径(公分)
      contactName: formData.contactName.trim(),
      phoneNumber: formData.contactPhone.trim(),
      address: formData.location.trim(),
      content: formData.content.trim(),
      quantity: formData.quantity, // 保存为文本，不再转换为数字
      province: formData.province || '', // 添加省份字段
      return: true, // 添加退林还耕标识字段，默认为true
      imageList: [], // 先创建空的图片列表，后续上传完图片再更新
      // 提供者信息
      provider: {
       
        sex: userInfo ? userInfo.sex : '',
        userId: userId || ''
      },
      createTime: new Date(),
      updateTime: new Date(),
      photoUpdated: new Date(), // 添加拍照更新时间字段
      status: 'active'
    };
    
    // 如果有经纬度信息，添加 location 字段
    if (longitude !== undefined && latitude !== undefined) {
      supplyData.location = db.Geo.Point(longitude, latitude)
    }
    
    // 保存到云数据库
    db.collection('supply_content').add({
      data: supplyData
    }).then(res => {
      const postId = res._id;
      
      // 创建通知记录
      this.createNoticeRecord(postId, formData.title.trim(), userId, userInfo);
      
      // 上传图片到指定文件夹
      this.uploadImages(userId, postId).then(async (fileIDs) => {
        // 更新帖子记录，添加图片列表
        if (fileIDs.length > 0) {
          db.collection('supply_content').doc(postId).update({
            data: {
              imageList: fileIDs,
              updateTime: new Date(),
              photoUpdated: new Date() // 添加拍照更新时间字段
            }
          }).then(() => {
            this.showSuccessAndReturn();
          }).catch(err => {
            console.error('更新图片列表失败:', err);
            this.showSuccessAndReturn();
          });
        } else {
          // 没有图片上传成功，执行回滚操作
          console.log('没有图片上传成功，执行回滚操作');

          wx.showLoading({
            title: '正在回滚...',
            mask: true
          });

          await this.rollbackPublish(postId, userId, userInfo);

          wx.hideLoading();
          wx.showToast({
            title: '发布失败，请添加图片后重试',
            icon: 'none',
            duration: 2000
          });
          this.setData({ submitting: false });
        }
      }).catch(async (err) => {
        console.error('上传图片失败:', err);

        // 执行回滚操作
        wx.showLoading({
          title: '正在回滚...',
          mask: true
        });

        await this.rollbackPublish(postId, userId, userInfo);

        wx.hideLoading();
        wx.showToast({
          title: '发布失败，请重试',
          icon: 'none',
          duration: 2000
        });
        this.setData({ submitting: false });
      });
    }).catch(err => {
      console.error('保存供应信息失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '发布失败，请重试',
        icon: 'none',
        duration: 2000
      });
      this.setData({ submitting: false });
    });
  },

  /**
   * 回滚发布操作 - 删除已创建的帖子和相关记录
   * @param {String} postId 帖子ID
   * @param {String} userId 用户ID
   * @param {Object} userInfo 用户信息
   */
  async rollbackPublish(postId, userId, userInfo) {
    const db = wx.cloud.database();

    try {
      // 1. 删除帖子记录
      await db.collection('supply_content').doc(postId).remove();
      console.log('已删除帖子记录:', postId);

      // 2. 删除通知记录
      await db.collection('notice').where({
        postId: postId,
        postType: 'supply'
      }).remove();
      console.log('已删除通知记录:', postId);

      // 3. 回滚发布统计数据
      await db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').update({
        data: {
          totalPost: db.command.inc(-1)
        }
      });
      console.log('已回滚发布统计数据');

      // 4. 回滚用户的supplyCount字段
      if (userId) {
        const userRes = await db.collection('users').doc(userId).get();
        const userData = userRes.data;

        if (userData.supplyCount !== undefined && userData.supplyCount > 0) {
          await db.collection('users').doc(userId).update({
            data: {
              supplyCount: db.command.inc(-1)
            }
          });

          // 更新全局数据中的supplyCount
          const app = getApp();
          if (app.globalData.userInfo && app.globalData.userInfo.supplyCount > 0) {
            app.globalData.userInfo.supplyCount -= 1;
            wx.setStorageSync('userInfo', app.globalData.userInfo);
          }

          console.log('已回滚用户供应数量');
        }
      }

    } catch (rollbackError) {
      console.error('回滚操作失败:', rollbackError);
      // 即使回滚失败，也要继续显示用户友好的错误信息
    }
  },

  /**
   * 创建通知记录
   * @param {String} postId 帖子ID
   * @param {String} plantName 植物名称
   * @param {String} publisherId 发布者ID
   * @param {Object} userInfo 用户信息
   */
  createNoticeRecord(postId, plantName, publisherId, userInfo) {
    // 获取数据库引用
    const db = wx.cloud.database();
    
   
    
    // 获取用户昵称，如果没有则使用默认值
    const publisherName = userInfo && userInfo.nickName ? userInfo.nickName : '用户';
    
    // 构建通知文本
    const noticeText = `${publisherName} 发布了一条 ${plantName} 退林还耕信息`;
    
    // 创建通知数据 - 确保字段格式与集合一致
    const noticeData = {
      postId: postId,             // 关联帖子ID
      postType: 'supply',         // 帖子类型: 'supply'
      publisherId: publisherId,     // 发布者ID
      publisherName: publisherName, // 发布者昵称
      plantName: plantName,       // 植物名称
      noticeText: noticeText,     // 通知文本
      return: true,               // 标记为退林还耕类型
      createTime: db.serverDate(), // 创建时间，使用服务器时间
      isPublic: true,             // 是否公开展示
      isRead: false,              // 是否已读
      isDeleted: false            // 是否已删除
    };
    
    // 在控制台打印将要创建的通知数据
    console.log('通知数据:', noticeData);
    
    // 直接向notice集合添加记录
    db.collection('notice').add({
      data: noticeData
    }).then(res => {
     
    }).catch(err => {
      console.error('创建通知失败，错误码:', err.errCode, '错误信息:', err.errMsg);
      
      // 根据错误代码提供更具体的信息
      if (err.errCode === -502005) {
        console.error('notice集合不存在，请在云开发控制台创建该集合');
      } else if (err.errCode === -502001) {
        console.error('notice集合权限不足，请检查集合权限设置');
      } else if (err.errCode === -502002) {
        console.error('notice集合字段验证失败，请检查数据格式');
      }
    });
  },

  /**
   * 显示成功提示并返回
   */
  showSuccessAndReturn() {
    wx.hideLoading();
    wx.showToast({
      title: '发布成功',
      icon: 'success',
      duration: 2000
    });
    
    const db = wx.cloud.database();
    const app = getApp();
    const userId = app.globalData.userId || wx.getStorageSync('userId');
    
    // 更新发布信息统计数据
    db.collection('appData').doc('61493796683c0eae01a1f4045d9b0e0b').update({
      data: {
        totalPost: db.command.inc(1)
      }
    }).catch(err => {
      console.error('更新发布统计数据失败:', err);
    });
    
    // 更新用户的supplyCount字段
    if (userId) {
      // 先查询用户信息，检查是否存在supplyCount字段
      db.collection('users').doc(userId).get().then(res => {
        const userData = res.data;
        
        // 构建更新数据对象
        let updateData = {};
        
        // 如果supplyCount字段存在，则增加1；否则创建该字段并设置为1
        if (userData.supplyCount !== undefined) {
          updateData.supplyCount = db.command.inc(1);
        } else {
          updateData.supplyCount = 1;
        }
        
        // 更新用户数据
        return db.collection('users').doc(userId).update({
          data: updateData
        });
      }).then(() => {
        console.log('用户供应数量更新成功');
        
        // 更新全局数据中的supplyCount
        if (app.globalData.userInfo) {
          if (app.globalData.userInfo.supplyCount !== undefined) {
            app.globalData.userInfo.supplyCount += 1;
          } else {
            app.globalData.userInfo.supplyCount = 1;
          }
          
          // 更新本地存储中的userInfo
          wx.setStorageSync('userInfo', app.globalData.userInfo);
        }
      }).catch(err => {
        console.error('更新用户供应数量失败:', err);
      });
    }

    // 延迟显示继续发布弹窗
    setTimeout(() => {
      this.setData({ showContinueModal: true });
    }, 2000);

    // 🛡️ 发布成功后5秒安全时间，再重置提交状态
    setTimeout(() => {
      this.setData({ submitting: false });
    }, 5000);
  },

  /**
   * 上传图片到云存储
   * @param {String} userId 用户ID
   * @param {String} postId 帖子ID
   * @returns {Promise<Array>} 返回上传成功的文件ID数组
   */
  uploadImages(userId, postId) {
    const { imageList } = this.data;
    
    // 如果没有图片，直接返回空数组
    if (imageList.length === 0) {
      return Promise.resolve([]);
    }
    
    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });
    
    // 创建文件夹路径
    const folderPath = `supply_images/${userId}_${postId}`;
    
    // 创建上传任务列表
    const uploadTasks = imageList.map((filePath, index) => {
      return new Promise((resolve, reject) => {
        // 获取文件扩展名
        const extension = filePath.match(/\.([^.]+)$/)[1] || 'jpg';
        
        // 生成云存储路径
        const cloudPath = `${folderPath}/image_${index}.${extension}`;
        
        // 上传文件
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: filePath,
          success: res => {
            // 返回文件ID
            resolve(res.fileID);
          },
          fail: err => {
            console.error('上传图片失败:', err);
            reject(err);
          }
        });
      });
    });
    
    // 等待所有上传任务完成
    return Promise.all(uploadTasks);
  },

  /**
   * 取消发布
   */
  cancelPublish() {
    // 如果用户已经输入了内容，提示确认
    const { formData, imageList } = this.data;
    const hasContent = formData.title || formData.content || formData.price || imageList.length > 0;
    
    if (hasContent) {
      wx.showModal({
        title: '确认取消',
        content: '您填写的内容将不会保存，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 初始化地区数据
   */
  initAreaData() {
    // 中国省份数据（删除台湾、香港、澳门）
    const provinces = {
      110000: '北京市',
      120000: '天津市',
      130000: '河北省',
      140000: '山西省',
      150000: '内蒙古自治区',
      210000: '辽宁省',
      220000: '吉林省',
      230000: '黑龙江省',
      310000: '上海市',
      320000: '江苏省',
      330000: '浙江省',
      340000: '安徽省',
      350000: '福建省',
      360000: '江西省',
      370000: '山东省',
      410000: '河南省',
      420000: '湖北省',
      430000: '湖南省',
      440000: '广东省',
      450000: '广西壮族自治区',
      460000: '海南省',
      500000: '重庆市',
      510000: '四川省',
      520000: '贵州省',
      530000: '云南省',
      540000: '西藏自治区',
      610000: '陕西省',
      620000: '甘肃省',
      630000: '青海省',
      640000: '宁夏回族自治区',
      650000: '新疆维吾尔自治区'
    };

    // 获取城市数据
    const cities = this.getCityData();

    // 初始化省份选项
    const provinceOptions = this.getOptions(provinces);
    this.setData({
      provinces: provinceOptions
    });

    // 初始化城市选项（默认选择第一个省份的城市）
    if (provinceOptions.length > 0) {
      const firstProvinceCode = provinceOptions[0].value;
      const cityOptions = this.getCities(firstProvinceCode, { cities });
      this.setData({
        cities: cityOptions
      });
    }
  },

  /**
   * 获取选项数组
   */
  getOptions(obj) {
    return Object.keys(obj).map((key) => ({ value: key, label: obj[key] }));
  },

  /**
   * 获取指定省份的城市列表
   */
  getCities(provinceCode, areaList) {
    const cityOptions = this.getOptions(areaList.cities);
    
    // 处理直辖市的情况（北京、天津、上海、重庆）
    if (provinceCode === '110000' || provinceCode === '120000' || 
        provinceCode === '310000' || provinceCode === '500000') {
      // 直辖市返回区县数据
      return cityOptions.filter(city => 
        city.value.toString().slice(0, 3) === provinceCode.toString().slice(0, 3)
      );
    }
    
    // 普通省份返回地级市数据
    return cityOptions.filter(city => {
      // 匹配省份编码前两位
      const matchProvince = city.value.toString().slice(0, 2) === provinceCode.toString().slice(0, 2);
      // 地级市编码通常是以00结尾的6位数，但也有特殊情况如省直辖县级市
      const isCity = city.value.toString().length === 6;
      return matchProvince && isCity;
    });
  },

  /**
   * 匹配前几位编码
   */
  match(value1, value2, size) {
    return value1.toString().slice(0, size) === value2.toString().slice(0, size);
  },

  /**
   * 打开地区选择器
   */
  onAreaPicker() {
    // 暂时注释掉手动选择地区功能，只保留使用当前位置
    // wx.showActionSheet({
    //   itemList: ['手动选择地区', '使用当前位置'],
    //   success: (res) => {
    //     if (res.tapIndex === 0) {
    //       // 手动选择地区
    //       this.setData({
    //         areaVisible: true
    //       });
    //     } else if (res.tapIndex === 1) {
    //       // 使用当前位置
    //       wx.showLoading({
    //         title: '获取位置中...',
    //         mask: true
    //       });
    //
    //       this.getCurrentLocationAndReverse();
    //     }
    //   }
    // });

    // 直接使用当前位置
    wx.showLoading({
      title: '获取位置中...',
      mask: true
    });

    this.getCurrentLocationAndReverse();
  },

  /**
   * 获取当前位置并逆地址解析
   */
  getCurrentLocationAndReverse() {
    mapUtils.getCurrentLocationAndReverse({
      success: (result) => {
        if (result.status === 0) {
          const addressComponent = result.result.address_component;
          const province = addressComponent.province;
          const city = addressComponent.city;
          const district = addressComponent.district;
          
          // 查找省份对应的code
          const provinces = this.data.provinces || [];
          let provinceCode = '';
          let cityCode = '';
          
          for (let i = 0; i < provinces.length; i++) {
            if (provinces[i].label.includes(province)) {
              provinceCode = provinces[i].value;
              break;
            }
          }
          
          // 如果找到省份代码，获取对应的城市列表
          if (provinceCode) {
            const cities = this.getCities(provinceCode, { cities: this.getCityData() });
            
            // 查找城市对应的code
            for (let i = 0; i < cities.length; i++) {
              if (cities[i].label.includes(city)) {
                cityCode = cities[i].value;
                break;
              }
            }
            
            // 更新地区选择器的值和显示文本
            if (provinceCode && cityCode) {
              const fullLocation = province + city + (district ? district : '');
              const displayText = province + ' ' + city + (district ? ' ' + district : '');
              
              this.setData({
                areaValue: [provinceCode, cityCode],
                areaText: displayText,
                'formData.location': fullLocation,
                cities: cities,
                'errors.location': '', // 清除地区相关的错误提示
                locationFailed: false // 定位成功，隐藏失败提示
              });

              wx.showToast({
                title: '已获取当前位置',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '无法匹配当前位置',
                icon: 'none'
              });
              // 设置定位失败状态
              this.setData({
                locationFailed: true
              });
            }
          } else {
            wx.showToast({
              title: '无法匹配当前位置',
              icon: 'none'
            });
            // 设置定位失败状态
            this.setData({
              locationFailed: true
            });
          }
        } else {
          // 定位失败，显示失败提示和刷新按钮
          this.setData({
            locationFailed: true
          });
        }
      },
      fail: (error) => {
        // 定位失败，显示失败提示和刷新按钮
        this.setData({
          locationFailed: true
        });

        // 根据错误类型显示不同的提示
        let errorMsg = '定位失败';
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          errorMsg = '请在设置中开启定位权限';
        } else if (error.errMsg && error.errMsg.includes('fail')) {
          errorMsg = '定位服务不可用，请检查GPS设置';
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  /**
   * 地区选择器取消事件
   */
  onPickerCancel() {
    this.setData({
      areaVisible: false
    });
  },

  /**
   * 地区选择器列变化事件
   */
  onColumnChange(e) {
    const { column, index } = e.detail;
    const { provinces } = this.data;

    if (column === 0) {
      // 更改省份时，更新城市列表
      const provinceCode = provinces[index].value;
      
      // 获取完整的城市数据
      const cityData = this.getCityData();
      
      // 获取当前选中省份的城市列表
      const cities = this.getCities(provinceCode, { cities: cityData });
      
      // 调试信息：打印当前选中的省份和对应的城市列表
    
      
      // 更新城市列表并重置选中的城市
      this.setData({
        cities: cities,
        areaValue: [provinceCode, cities.length > 0 ? cities[0].value : '']
      });
    }
  },

  /**
   * 获取完整的城市数据
   */
  getCityData() {
    return {
      // 北京市辖区
      110101: '东城区',
      110102: '西城区',
      110105: '朝阳区',
      110106: '丰台区',
      110107: '石景山区',
      110108: '海淀区',
      110109: '门头沟区',
      110111: '房山区',
      110112: '通州区',
      110113: '顺义区',
      110114: '昌平区',
      110115: '大兴区',
      110116: '怀柔区',
      110117: '平谷区',
      110118: '密云区',
      110119: '延庆区',
      
      // 天津市辖区
      120101: '和平区',
      120102: '河东区',
      120103: '河西区',
      120104: '南开区',
      120105: '河北区',
      120106: '红桥区',
      120110: '东丽区',
      120111: '西青区',
      120112: '津南区',
      120113: '北辰区',
      120114: '武清区',
      120115: '宝坻区',
      120116: '滨海新区',
      120117: '宁河区',
      120118: '静海区',
      120119: '蓟州区',
      
      // 河北省
      130100: '石家庄市',
      130200: '唐山市',
      130300: '秦皇岛市',
      130400: '邯郸市',
      130500: '邢台市',
      130600: '保定市',
      130700: '张家口市',
      130800: '承德市',
      130900: '沧州市',
      131000: '廊坊市',
      131100: '衡水市',
      
      // 山西省
      140100: '太原市',
      140200: '大同市',
      140300: '阳泉市',
      140400: '长治市',
      140500: '晋城市',
      140600: '朔州市',
      140700: '晋中市',
      140800: '运城市',
      140900: '忻州市',
      141000: '临汾市',
      141100: '吕梁市',
      
      // 内蒙古自治区
      150100: '呼和浩特市',
      150200: '包头市',
      150300: '乌海市',
      150400: '赤峰市',
      150500: '通辽市',
      150600: '鄂尔多斯市',
      150700: '呼伦贝尔市',
      150800: '巴彦淖尔市',
      150900: '乌兰察布市',
      152200: '兴安盟',
      152500: '锡林郭勒盟',
      152900: '阿拉善盟',
      
      // 辽宁省
      210100: '沈阳市',
      210200: '大连市',
      210300: '鞍山市',
      210400: '抚顺市',
      210500: '本溪市',
      210600: '丹东市',
      210700: '锦州市',
      210800: '营口市',
      210900: '阜新市',
      211000: '辽阳市',
      211100: '盘锦市',
      211200: '铁岭市',
      211300: '朝阳市',
      211400: '葫芦岛市',
      
      // 吉林省
      220100: '长春市',
      220200: '吉林市',
      220300: '四平市',
      220400: '辽源市',
      220500: '通化市',
      220600: '白山市',
      220700: '松原市',
      220800: '白城市',
      222400: '延边朝鲜族自治州',
      
      // 黑龙江省
      230100: '哈尔滨市',
      230200: '齐齐哈尔市',
      230300: '鸡西市',
      230400: '鹤岗市',
      230500: '双鸭山市',
      230600: '大庆市',
      230700: '伊春市',
      230800: '佳木斯市',
      230900: '七台河市',
      231000: '牡丹江市',
      231100: '黑河市',
      231200: '绥化市',
      232700: '大兴安岭地区',
      
      // 上海市辖区
      310101: '黄浦区',
      310104: '徐汇区',
      310105: '长宁区',
      310106: '静安区',
      310107: '普陀区',
      310109: '虹口区',
      310110: '杨浦区',
      310112: '闵行区',
      310113: '宝山区',
      310114: '嘉定区',
      310115: '浦东新区',
      310116: '金山区',
      310117: '松江区',
      310118: '青浦区',
      310120: '奉贤区',
      310151: '崇明区',
      
      // 江苏省
      320100: '南京市',
      320200: '无锡市',
      320300: '徐州市',
      320400: '常州市',
      320500: '苏州市',
      320600: '南通市',
      320700: '连云港市',
      320800: '淮安市',
      320900: '盐城市',
      321000: '扬州市',
      321100: '镇江市',
      321200: '泰州市',
      321300: '宿迁市',
      
      // 浙江省
      330100: '杭州市',
      330200: '宁波市',
      330300: '温州市',
      330400: '嘉兴市',
      330500: '湖州市',
      330600: '绍兴市',
      330700: '金华市',
      330800: '衢州市',
      330900: '舟山市',
      331000: '台州市',
      331100: '丽水市',
      
      // 安徽省
      340100: '合肥市',
      340200: '芜湖市',
      340300: '蚌埠市',
      340400: '淮南市',
      340500: '马鞍山市',
      340600: '淮北市',
      340700: '铜陵市',
      340800: '安庆市',
      341000: '黄山市',
      341100: '滁州市',
      341200: '阜阳市',
      341300: '宿州市',
      341500: '六安市',
      341600: '亳州市',
      341700: '池州市',
      341800: '宣城市',
      
      // 福建省
      350100: '福州市',
      350200: '厦门市',
      350300: '莆田市',
      350400: '三明市',
      350500: '泉州市',
      350600: '漳州市',
      350700: '南平市',
      350800: '龙岩市',
      350900: '宁德市',
      
      // 江西省
      360100: '南昌市',
      360200: '景德镇市',
      360300: '萍乡市',
      360400: '九江市',
      360500: '新余市',
      360600: '鹰潭市',
      360700: '赣州市',
      360800: '吉安市',
      360900: '宜春市',
      361000: '抚州市',
      361100: '上饶市',
      
      // 山东省
      370100: '济南市',
      370200: '青岛市',
      370300: '淄博市',
      370400: '枣庄市',
      370500: '东营市',
      370600: '烟台市',
      370700: '潍坊市',
      370800: '济宁市',
      370900: '泰安市',
      371000: '威海市',
      371100: '日照市',
      371300: '临沂市',
      371400: '德州市',
      371500: '聊城市',
      371600: '滨州市',
      371700: '菏泽市',
      
      // 河南省
      410100: '郑州市',
      410200: '开封市',
      410300: '洛阳市',
      410400: '平顶山市',
      410500: '安阳市',
      410600: '鹤壁市',
      410700: '新乡市',
      410800: '焦作市',
      410900: '濮阳市',
      411000: '许昌市',
      411100: '漯河市',
      411200: '三门峡市',
      411300: '南阳市',
      411400: '商丘市',
      411500: '信阳市',
      411600: '周口市',
      411700: '驻马店市',
      419001: '济源市',
      
      // 湖北省
      420100: '武汉市',
      420200: '黄石市',
      420300: '十堰市',
      420500: '宜昌市',
      420600: '襄阳市',
      420700: '鄂州市',
      420800: '荆门市',
      420900: '孝感市',
      421000: '荆州市',
      421100: '黄冈市',
      421200: '咸宁市',
      421300: '随州市',
      422800: '恩施土家族苗族自治州',
      429004: '仙桃市',
      429005: '潜江市',
      429006: '天门市',
      429021: '神农架林区',
      
      // 湖南省
      430100: '长沙市',
      430200: '株洲市',
      430300: '湘潭市',
      430400: '衡阳市',
      430500: '邵阳市',
      430600: '岳阳市',
      430700: '常德市',
      430800: '张家界市',
      430900: '益阳市',
      431000: '郴州市',
      431100: '永州市',
      431200: '怀化市',
      431300: '娄底市',
      433100: '湘西土家族苗族自治州',
      
      // 广东省
      440100: '广州市',
      440200: '韶关市',
      440300: '深圳市',
      440400: '珠海市',
      440500: '汕头市',
      440600: '佛山市',
      440700: '江门市',
      440800: '湛江市',
      440900: '茂名市',
      441200: '肇庆市',
      441300: '惠州市',
      441400: '梅州市',
      441500: '汕尾市',
      441600: '河源市',
      441700: '阳江市',
      441800: '清远市',
      441900: '东莞市',
      442000: '中山市',
      445100: '潮州市',
      445200: '揭阳市',
      445300: '云浮市',
      
      // 广西壮族自治区
      450100: '南宁市',
      450200: '柳州市',
      450300: '桂林市',
      450400: '梧州市',
      450500: '北海市',
      450600: '防城港市',
      450700: '钦州市',
      450800: '贵港市',
      450900: '玉林市',
      451000: '百色市',
      451100: '贺州市',
      451200: '河池市',
      451300: '来宾市',
      451400: '崇左市',
      
      // 海南省
      460100: '海口市',
      460200: '三亚市',
      460300: '三沙市',
      460400: '儋州市',
      469001: '五指山市',
      469002: '琼海市',
      469005: '文昌市',
      469006: '万宁市',
      469007: '东方市',
      469021: '定安县',
      469022: '屯昌县',
      469023: '澄迈县',
      469024: '临高县',
      469025: '白沙黎族自治县',
      469026: '昌江黎族自治县',
      469027: '乐东黎族自治县',
      469028: '陵水黎族自治县',
      469029: '保亭黎族苗族自治县',
      469030: '琼中黎族苗族自治县',
      
      // 重庆市辖区和县
      500101: '万州区',
      500102: '涪陵区',
      500103: '渝中区',
      500104: '大渡口区',
      500105: '江北区',
      500106: '沙坪坝区',
      500107: '九龙坡区',
      500108: '南岸区',
      500109: '北碚区',
      500110: '綦江区',
      500111: '大足区',
      500112: '渝北区',
      500113: '巴南区',
      500114: '黔江区',
      500115: '长寿区',
      500116: '江津区',
      500117: '合川区',
      500118: '永川区',
      500119: '南川区',
      500120: '璧山区',
      500151: '铜梁区',
      500152: '潼南区',
      500153: '荣昌区',
      500154: '开州区',
      500155: '梁平区',
      500156: '武隆区',
      500229: '城口县',
      500230: '丰都县',
      500231: '垫江县',
      500233: '忠县',
      500235: '云阳县',
      500236: '奉节县',
      500237: '巫山县',
      500238: '巫溪县',
      500240: '石柱土家族自治县',
      500241: '秀山土家族苗族自治县',
      500242: '酉阳土家族苗族自治县',
      500243: '彭水苗族土家族自治县',
      
      // 四川省
      510100: '成都市',
      510300: '自贡市',
      510400: '攀枝花市',
      510500: '泸州市',
      510600: '德阳市',
      510700: '绵阳市',
      510800: '广元市',
      510900: '遂宁市',
      511000: '内江市',
      511100: '乐山市',
      511300: '南充市',
      511400: '眉山市',
      511500: '宜宾市',
      511600: '广安市',
      511700: '达州市',
      511800: '雅安市',
      511900: '巴中市',
      512000: '资阳市',
      513200: '阿坝藏族羌族自治州',
      513300: '甘孜藏族自治州',
      513400: '凉山彝族自治州',
      
      // 贵州省
      520100: '贵阳市',
      520200: '六盘水市',
      520300: '遵义市',
      520400: '安顺市',
      520500: '毕节市',
      520600: '铜仁市',
      522300: '黔西南布依族苗族自治州',
      522600: '黔东南苗族侗族自治州',
      522700: '黔南布依族苗族自治州',
      
      // 云南省
      530100: '昆明市',
      530300: '曲靖市',
      530400: '玉溪市',
      530500: '保山市',
      530600: '昭通市',
      530700: '丽江市',
      530800: '普洱市',
      530900: '临沧市',
      532300: '楚雄彝族自治州',
      532500: '红河哈尼族彝族自治州',
      532600: '文山壮族苗族自治州',
      532800: '西双版纳傣族自治州',
      532900: '大理白族自治州',
      533100: '德宏傣族景颇族自治州',
      533300: '怒江傈僳族自治州',
      533400: '迪庆藏族自治州',
      
      // 西藏自治区
      540100: '拉萨市',
      540200: '日喀则市',
      540300: '昌都市',
      540400: '林芝市',
      540500: '山南市',
      540600: '那曲市',
      542500: '阿里地区',
      
      // 陕西省
      610100: '西安市',
      610200: '铜川市',
      610300: '宝鸡市',
      610400: '咸阳市',
      610500: '渭南市',
      610600: '延安市',
      610700: '汉中市',
      610800: '榆林市',
      610900: '安康市',
      611000: '商洛市',
      
      // 甘肃省
      620100: '兰州市',
      620200: '嘉峪关市',
      620300: '金昌市',
      620400: '白银市',
      620500: '天水市',
      620600: '武威市',
      620700: '张掖市',
      620800: '平凉市',
      620900: '酒泉市',
      621000: '庆阳市',
      621100: '定西市',
      621200: '陇南市',
      622900: '临夏回族自治州',
      623000: '甘南藏族自治州',
      
      // 青海省
      630100: '西宁市',
      630200: '海东市',
      632200: '海北藏族自治州',
      632300: '黄南藏族自治州',
      632500: '海南藏族自治州',
      632600: '果洛藏族自治州',
      632700: '玉树藏族自治州',
      632800: '海西蒙古族藏族自治州',
      
      // 宁夏回族自治区
      640100: '银川市',
      640200: '石嘴山市',
      640300: '吴忠市',
      640400: '固原市',
      640500: '中卫市',
      
      // 新疆维吾尔自治区
      650100: '乌鲁木齐市',
      650200: '克拉玛依市',
      650400: '吐鲁番市',
      650500: '哈密市',
      652300: '昌吉回族自治州',
      652700: '博尔塔拉蒙古自治州',
      652800: '巴音郭楞蒙古自治州',
      652900: '阿克苏地区',
      653000: '克孜勒苏柯尔克孜自治州',
      653100: '喀什地区',
      653200: '和田地区',
      654000: '伊犁哈萨克自治州',
      654200: '塔城地区',
      654300: '阿勒泰地区',
      659001: '石河子市',
      659002: '阿拉尔市',
      659003: '图木舒克市',
      659004: '五家渠市',
      659005: '北屯市',
      659006: '铁门关市',
      659007: '双河市',
      659008: '可克达拉市',
      659009: '昆玉市',
      659010: '胡杨河市'
    };
  },

  /**
   * 设置默认地区值
   */
  setDefaultArea() {
    // 如果已经有地区数据，则不需要设置默认值
    if (this.data.areaText) return;
    
    // 不设置默认值，保持为空
    this.setData({
      areaValue: [],
      areaText: '',
      'formData.location': ''
    });
  },

  /**
   * 地区选择器确认事件
   */
  onPickerChange(e) {
    const { value, label } = e.detail;
    
    // 确保有完整的省市选择
    if (value.length < 2 || !value[0] || !value[1]) {
      wx.showToast({
        title: '请选择完整的省市信息',
        icon: 'none'
      });
      return;
    }
    
    // 将选择的地区保存到表单数据中
    const location = label.join('');
    
    this.setData({
      areaVisible: false,
      areaValue: value,
      areaText: label.join(' '),
      'formData.location': location,
      'errors.location': ''
    });
  },

  /**
   * 处理价格单位选择
   */
  onPriceUnitChange(e) {
    this.setData({
      'formData.price_unit': this.data.priceUnitOptions[e.detail.value],
      'errors.price_unit': ''
    });
  },

  /**
   * 处理栽培状态选择
   */
  onPlantMethodChange(e) {
    this.setData({
      'formData.plant_method': this.data.plantMethodOptions[e.detail.value],
      'errors.plant_method': ''
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 仅用于阻止事件冒泡
    return false;
  },

  /**
   * 隐藏继续发布弹窗
   */
  hideContinueModal() {
    this.setData({ showContinueModal: false });
  },

  /**
   * 取消继续发布，返回首页
   */
  cancelContinue() {
    this.setData({ showContinueModal: false });
    wx.switchTab({
      url: '/pages/supply/supply'
    });
  },

  /**
   * 确认继续发布
   */
  confirmContinue() {
    this.setData({ showContinueModal: false });

    // 保存当前的联系电话和位置信息
    const currentPhone = this.data.formData.contactPhone;
    const currentLocation = this.data.formData.location;
    const currentAreaText = this.data.areaText;
    const currentAreaValue = this.data.areaValue;

    // 重新打开发布页面（重置表单但保留电话和位置）
    wx.redirectTo({
      url: `/pages/returnPublic/returnPublic?phone=${currentPhone}&location=${encodeURIComponent(currentLocation)}&areaText=${encodeURIComponent(currentAreaText)}&areaValue=${JSON.stringify(currentAreaValue)}`
    });
  }
});