#!/usr/bin/env node

/**
 * 快速配置脚本
 * 用于重新部署后快速设置配置文件
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始配置 payment-server...');

// 创建必要的目录
const dirs = ['logs', 'certs'];
dirs.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ 创建目录: ${dir}`);
  }
});

// 检查.env文件
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('❌ .env文件不存在');
  console.log('请手动创建.env文件，参考.env.example');
} else {
  console.log('✅ .env文件存在');
}

// 检查私钥文件
const keyPaths = [
  path.join(__dirname, 'certs', 'apiclient_key.pem'),
  path.join(__dirname, 'apiclient_key.pem')
];

let keyFound = false;
keyPaths.forEach(keyPath => {
  if (fs.existsSync(keyPath)) {
    console.log(`✅ 找到私钥文件: ${keyPath}`);
    keyFound = true;
  }
});

if (!keyFound) {
  console.log('❌ 私钥文件不存在');
  console.log('请上传 apiclient_key.pem 到以下位置之一:');
  keyPaths.forEach(p => console.log(`  - ${p}`));
}

// 检查package.json依赖
const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const requiredDeps = ['@cloudbase/node-sdk', 'express', 'winston'];
  
  console.log('\n📦 检查依赖:');
  requiredDeps.forEach(dep => {
    if (pkg.dependencies && pkg.dependencies[dep]) {
      console.log(`✅ ${dep}: ${pkg.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep}: 未安装`);
    }
  });
}

console.log('\n🔧 配置检查完成!');
console.log('\n下一步:');
console.log('1. 确保.env文件配置正确');
console.log('2. 上传私钥文件到certs目录');
console.log('3. 运行: npm install');
console.log('4. 重启服务器');
