const axios = require('axios');
const config = require('../config/config');
const logger = require('../utils/logger');

/**
 * 微信服务类
 */
class WechatService {
  /**
   * 通过code获取用户openid和session_key
   * @param {string} code 微信登录code
   * @returns {object} 包含openid和session_key的对象
   */
  async getOpenidByCode(code) {
    try {
      const url = 'https://api.weixin.qq.com/sns/jscode2session';
      const params = {
        appid: config.wechat.appid,
        secret: config.wechat.secret,
        js_code: code,
        grant_type: 'authorization_code'
      };

      const response = await axios.get(url, { params });
      const data = response.data;

      if (data.errcode) {
        throw new Error(`WeChat API error: ${data.errcode} - ${data.errmsg}`);
      }

      logger.info('Successfully got openid from WeChat', { 
        openid: data.openid?.substring(0, 10) + '...' 
      });

      return {
        openid: data.openid,
        session_key: data.session_key,
        unionid: data.unionid
      };
    } catch (error) {
      logger.error('Failed to get openid from WeChat:', error);
      throw new Error('Failed to authenticate with WeChat');
    }
  }

  /**
   * 获取微信access_token
   * @returns {string} access_token
   */
  async getAccessToken() {
    try {
      const url = 'https://api.weixin.qq.com/cgi-bin/token';
      const params = {
        grant_type: 'client_credential',
        appid: config.wechat.appid,
        secret: config.wechat.secret
      };

      const response = await axios.get(url, { params });
      const data = response.data;

      if (data.errcode) {
        throw new Error(`WeChat API error: ${data.errcode} - ${data.errmsg}`);
      }

      return data.access_token;
    } catch (error) {
      logger.error('Failed to get access token from WeChat:', error);
      throw new Error('Failed to get WeChat access token');
    }
  }

  /**
   * 验证用户信息签名
   * @param {object} userInfo 用户信息
   * @param {string} signature 签名
   * @param {string} sessionKey session_key
   * @returns {boolean} 验证结果
   */
  verifyUserInfo(userInfo, signature, sessionKey) {
    const crypto = require('crypto');
    
    try {
      const rawData = JSON.stringify(userInfo);
      const hash = crypto.createHmac('sha1', sessionKey).update(rawData).digest('hex');
      return hash === signature;
    } catch (error) {
      logger.error('Failed to verify user info signature:', error);
      return false;
    }
  }
}

module.exports = new WechatService();
