const config = require('../config/config');
const logger = require('../utils/logger');
const cloudDatabaseService = require('./cloudDatabaseService'); // 使用官方SDK

/**
 * 数据库服务类
 * 统一的数据库操作接口，使用微信云开发数据库
 */
class DatabaseService {
  constructor() {
    this.useCloudDatabase = config.cloudDatabase && config.cloudDatabase.enabled;
    logger.info('Database service initialized', {
      useCloudDatabase: this.useCloudDatabase,
      envId: config.cloudDatabase?.envId
    });
  }

  /**
   * 创建积分日志记录
   */
  async createPointLog(logData) {
    try {
      logger.info('Creating point log:', {
        order_no: logData.orderNo,
        amount: logData.points
      });

      if (this.useCloudDatabase) {
        return await cloudDatabaseService.createPointLog(logData);
      } else {
        throw new Error('Cloud database not configured');
      }
    } catch (error) {
      logger.error('Failed to create point log:', error);
      throw error;
    }
  }

  /**
   * 更新用户积分
   */
  async updateUserPoints(openid, points) {
    try {
      logger.info('Updating user points:', {
        openid: openid?.substring(0, 10) + '...',
        points
      });

      if (this.useCloudDatabase) {
        return await cloudDatabaseService.updateUserPoints(openid, points);
      } else {
        throw new Error('Cloud database not configured');
      }
    } catch (error) {
      logger.error('Failed to update user points:', error);
      throw error;
    }
  }

  /**
   * 查询用户信息
   */
  async getUserByOpenid(openid) {
    try {
      logger.info('Querying user by openid:', {
        openid: openid?.substring(0, 10) + '...'
      });

      if (this.useCloudDatabase) {
        return await cloudDatabaseService.getUserByOpenid(openid);
      } else {
        throw new Error('Cloud database not configured');
      }
    } catch (error) {
      logger.error('Failed to query user:', error);
      throw error;
    }
  }

  /**
   * 检查订单是否已存在
   */
  async checkOrderExists(orderNo) {
    try {
      logger.info('Checking order exists:', { orderNo });

      if (this.useCloudDatabase) {
        const result = await cloudDatabaseService.checkOrderExists(orderNo);
        logger.info('Order exists check result:', { orderNo, exists: result });
        return result;
      } else {
        throw new Error('Cloud database not configured');
      }
    } catch (error) {
      logger.error('Failed to check order exists:', error);
      throw error;
    }
  }

  /**
   * 更新积分日志状态
   */
  async updatePointLogStatus(orderNo, status, transactionId = null) {
    try {
      logger.info('Updating point log status:', {
        orderNo,
        status,
        transactionId
      });

      if (this.useCloudDatabase) {
        return await cloudDatabaseService.updatePointLogStatus(orderNo, status, transactionId);
      } else {
        throw new Error('Cloud database not configured');
      }
    } catch (error) {
      logger.error('Failed to update point log status:', error);
      throw error;
    }
  }


}

module.exports = new DatabaseService();
