const { body, param, validationResult } = require('express-validator');

/**
 * 处理验证错误
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

/**
 * 创建支付订单验证规则
 */
const validateCreatePayment = [
  body('amount')
    .isFloat({ min: 0.01, max: 10000 })
    .withMessage('Amount must be between 0.01 and 10000'),
  body('points')
    .isInt({ min: 1, max: 10000 })
    .withMessage('Points must be between 1 and 10000'),
  body('description')
    .optional()
    .isString()
    .isLength({ max: 200 })
    .withMessage('Description must be a string with max 200 characters'),
  handleValidationErrors
];

/**
 * 查询支付订单验证规则
 */
const validateQueryPayment = [
  param('orderNo')
    .isString()
    .isLength({ min: 10, max: 50 })
    .withMessage('Order number must be a string between 10 and 50 characters'),
  handleValidationErrors
];

/**
 * 微信登录验证规则
 */
const validateWechatLogin = [
  body('code')
    .isString()
    .isLength({ min: 1 })
    .withMessage('WeChat code is required'),
  body('userInfo')
    .optional()
    .isObject()
    .withMessage('User info must be an object'),
  handleValidationErrors
];

/**
 * 验证支付结果验证规则
 */
const validateVerifyPayment = [
  body('orderNo')
    .isString()
    .isLength({ min: 10, max: 50 })
    .withMessage('Order number must be a string between 10 and 50 characters'),
  body('expectedPoints')
    .isInt({ min: 1 })
    .withMessage('Expected points must be a positive integer'),
  handleValidationErrors
];

module.exports = {
  validateCreatePayment,
  validateQueryPayment,
  validateWechatLogin,
  validateVerifyPayment,
  handleValidationErrors
};
