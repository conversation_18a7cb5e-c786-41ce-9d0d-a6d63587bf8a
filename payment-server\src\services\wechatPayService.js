const https = require('https');
const config = require('../config/config');
const logger = require('../utils/logger');
const { 
  generateOrderNo, 
  buildWechatPayAuthHeader, 
  generateMiniProgramPayParams 
} = require('../utils/crypto');

/**
 * 微信支付服务类
 */
class WechatPayService {
  /**
   * 创建支付订单
   * @param {object} orderData 订单数据
   * @returns {object} 支付参数
   */
  async createPaymentOrder(orderData) {
    try {
      const { amount, points, description, openid } = orderData;
      
      // 生成订单号
      const out_trade_no = generateOrderNo('RECHARGE');
      
      // 构建微信支付订单数据
      const wechatOrderData = {
        appid: config.wechatPay.appid,
        mchid: config.wechatPay.mchid,
        description: description || `积分充值${points}积分`,
        out_trade_no: out_trade_no,
        notify_url: config.wechatPay.notify_url,
        amount: {
          total: Math.round(amount * 100), // 金额单位：分
          currency: 'CNY'
        },
        payer: {
          openid: openid
        }
      };

      logger.info('Creating WeChat payment order:', { 
        out_trade_no, 
        amount, 
        points,
        openid: openid?.substring(0, 10) + '...'
      });

      // 调用微信支付API
      const payResult = await this.callWechatPayAPI('/v3/pay/transactions/jsapi', 'POST', wechatOrderData);
      
      // 生成小程序支付参数
      const payParams = generateMiniProgramPayParams(
        config.wechatPay.appid, 
        payResult.prepay_id, 
        config.wechatPay.private_key
      );

      return {
        success: true,
        out_trade_no: out_trade_no,
        ...payParams
      };
    } catch (error) {
      logger.error('Failed to create payment order:', error);
      throw error;
    }
  }

  /**
   * 查询支付订单状态
   * @param {string} out_trade_no 商户订单号
   * @returns {object} 订单状态信息
   */
  async queryPaymentOrder(out_trade_no) {
    try {
      const url = `/v3/pay/transactions/out-trade-no/${out_trade_no}?mchid=${config.wechatPay.mchid}`;
      const result = await this.callWechatPayAPI(url, 'GET');
      
      return {
        success: true,
        data: {
          out_trade_no: result.out_trade_no,
          transaction_id: result.transaction_id,
          trade_state: result.trade_state,
          trade_state_desc: result.trade_state_desc,
          amount: result.amount,
          success_time: result.success_time
        }
      };
    } catch (error) {
      logger.error('Failed to query payment order:', error);
      throw error;
    }
  }

  /**
   * 调用微信支付API
   * @param {string} url API路径
   * @param {string} method HTTP方法
   * @param {object} data 请求数据
   * @returns {object} API响应
   */
  async callWechatPayAPI(url, method, data = null) {
    return new Promise((resolve, reject) => {
      const body = data ? JSON.stringify(data) : '';
      const authorization = buildWechatPayAuthHeader(method, url, body, config.wechatPay);
      
      const options = {
        hostname: 'api.mch.weixin.qq.com',
        port: 443,
        path: url,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'WeChatPay-APIv3-SDK/Node.js',
          'Authorization': authorization
        }
      };

      if (body) {
        options.headers['Content-Length'] = Buffer.byteLength(body);
      }
      
      const req = https.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          try {
            const result = JSON.parse(responseData);
            
            if (res.statusCode === 200) {
              logger.info('WeChat Pay API success:', { 
                url, 
                method, 
                statusCode: res.statusCode 
              });
              resolve(result);
            } else {
              logger.error('WeChat Pay API error:', { 
                url, 
                method, 
                statusCode: res.statusCode, 
                response: result 
              });
              reject(new Error(`微信支付API调用失败: ${responseData}`));
            }
          } catch (error) {
            logger.error('Failed to parse WeChat Pay API response:', error);
            reject(new Error(`解析响应失败: ${error.message}`));
          }
        });
      });
      
      req.on('error', (error) => {
        logger.error('WeChat Pay API request error:', error);
        reject(error);
      });
      
      if (body) {
        req.write(body);
      }
      req.end();
    });
  }
}

module.exports = new WechatPayService();
