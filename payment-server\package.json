{"name": "payment-server", "version": "1.0.0", "description": "Node.js支付服务后端", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop payment-server", "pm2:restart": "pm2 restart payment-server"}, "keywords": ["payment", "wechat-pay", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "axios": "^1.6.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}