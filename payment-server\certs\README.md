# 证书文件目录

## 文件说明

请将您的微信支付证书文件放置在此目录下：

### 必需文件
- `apiclient_key.pem` - 微信支付商户私钥文件

### 可选文件
- `apiclient_cert.pem` - 微信支付商户证书文件（如果需要）
- `wechatpay_cert.pem` - 微信支付平台证书文件（如果需要）

## 安全注意事项

1. **文件权限**：确保证书文件权限设置为 600 或 400
   ```bash
   chmod 600 apiclient_key.pem
   ```

2. **Git忽略**：证书文件已在 .gitignore 中配置，不会被提交到版本控制

3. **备份**：请妥善保管证书文件的备份

## 文件获取

1. 登录微信商户平台
2. 进入 `账户中心` → `API安全`
3. 下载商户证书文件
4. 将 `apiclient_key.pem` 上传到此目录

## 验证配置

上传文件后，重启应用，查看日志确认私钥加载成功：
```bash
pm2 restart payment-server
pm2 logs payment-server
```

应该看到类似日志：
```
Loading private key from file: /www/wwwroot/payment-server/certs/apiclient_key.pem
Private key loaded successfully from file
```
