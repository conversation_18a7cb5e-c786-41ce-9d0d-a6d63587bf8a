const crypto = require('crypto');

/**
 * 生成随机字符串
 * @param {number} length 字符串长度
 * @returns {string} 随机字符串
 */
function generateNonceStr(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成订单号
 * @param {string} prefix 前缀
 * @returns {string} 订单号
 */
function generateOrderNo(prefix = 'RECHARGE') {
  const now = new Date();
  const timestamp = now.getTime();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}_${timestamp}_${random}`;
}

/**
 * 生成微信支付签名
 * @param {string} method HTTP方法
 * @param {string} url 请求URL
 * @param {number} timestamp 时间戳
 * @param {string} nonce_str 随机字符串
 * @param {string} body 请求体
 * @param {string} privateKey 私钥
 * @returns {string} 签名
 */
function generateWechatPaySignature(method, url, timestamp, nonce_str, body, privateKey) {
  const message = `${method}\n${url}\n${timestamp}\n${nonce_str}\n${body}\n`;
  
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(message);
  return sign.sign(privateKey, 'base64');
}

/**
 * 构建微信支付Authorization头
 * @param {string} method HTTP方法
 * @param {string} url 请求URL
 * @param {string} body 请求体
 * @param {object} config 配置对象
 * @returns {string} Authorization头
 */
function buildWechatPayAuthHeader(method, url, body, config) {
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce_str = generateNonceStr();
  const signature = generateWechatPaySignature(method, url, timestamp, nonce_str, body, config.private_key);
  
  return `WECHATPAY2-SHA256-RSA2048 mchid="${config.mchid}",nonce_str="${nonce_str}",signature="${signature}",timestamp="${timestamp}",serial_no="${config.serial_no}"`;
}

/**
 * 生成小程序支付参数签名
 * @param {string} appid 小程序appid
 * @param {string} prepay_id 预支付交易会话标识
 * @param {string} privateKey 私钥
 * @returns {object} 支付参数
 */
function generateMiniProgramPayParams(appid, prepay_id, privateKey) {
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const nonce_str = generateNonceStr();
  const package_str = `prepay_id=${prepay_id}`;
  
  // 构建签名字符串
  const signStr = `${appid}\n${timestamp}\n${nonce_str}\n${package_str}\n`;
  
  // 生成签名
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(signStr);
  const paySign = sign.sign(privateKey, 'base64');
  
  return {
    timeStamp: timestamp,
    nonceStr: nonce_str,
    package: package_str,
    signType: 'RSA',
    paySign: paySign
  };
}

/**
 * 解密微信支付回调数据
 * @param {string} ciphertext 密文
 * @param {string} associated_data 附加数据
 * @param {string} nonce 随机串
 * @param {string} apiv3_key APIv3密钥
 * @returns {object} 解密后的数据
 */
function decryptWechatPayCallback(ciphertext, associated_data, nonce, apiv3_key) {
  try {
    const key = Buffer.from(apiv3_key, 'utf8');
    const iv = Buffer.from(nonce, 'utf8');
    const encrypted = Buffer.from(ciphertext, 'base64');
    const authTag = encrypted.slice(-16);
    const data = encrypted.slice(0, -16);

    const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
    decipher.setAuthTag(authTag);
    decipher.setAAD(Buffer.from(associated_data, 'utf8'));

    let decrypted = decipher.update(data, null, 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  } catch (error) {
    throw new Error(`解密失败: ${error.message}`);
  }
}

/**
 * 验证微信支付回调签名
 * @param {string} timestamp 时间戳
 * @param {string} nonce 随机串
 * @param {string} body 请求体
 * @param {string} signature 签名
 * @param {string} publicKey 微信支付平台公钥
 * @returns {boolean} 验证结果
 */
function verifyWechatPaySignature(timestamp, nonce, body, signature, publicKey) {
  try {
    const message = `${timestamp}\n${nonce}\n${body}\n`;
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(message);
    return verify.verify(publicKey, signature, 'base64');
  } catch (error) {
    return false;
  }
}

module.exports = {
  generateNonceStr,
  generateOrderNo,
  generateWechatPaySignature,
  buildWechatPayAuthHeader,
  generateMiniProgramPayParams,
  decryptWechatPayCallback,
  verifyWechatPaySignature
};
